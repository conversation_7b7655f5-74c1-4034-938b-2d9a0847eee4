// // import 'dart:async';
// // import 'dart:convert';
// // import 'dart:developer';
// // import 'package:http/http.dart' as http;
// // import 'package:flutter/material.dart';
// // import 'package:get/get.dart';
// // import 'package:shared_preferences/shared_preferences.dart';
// // import 'package:smartsewa/core/development/console.dart';
// // import 'package:smartsewa/network/base_client.dart';
// // import 'package:smartsewa/network/services/authServices/auth_controller.dart';
// // import 'package:smartsewa/views/user_screen/drawer%20screen/map_Screen.dart';
// // import 'package:smartsewa/views/user_screen/main_screen.dart';
// // import 'package:smartsewa/views/widgets/custom_toasts.dart';
// // import '../../../network/models/servicemodel.dart';
// // import '../../widgets/my_appbar.dart';

// // class ServicesScreen extends StatefulWidget {
// //   const ServicesScreen({
// //     super.key,
// //     required this.id,
// //     required this.name,
// //   });
// //   final String id;
// //   final String name;

// //   @override
// //   State<ServicesScreen> createState() => _ServicesScreenState();
// // }

// // class _ServicesScreenState extends State<ServicesScreen> {
// //   var sid;

// //   final controller = Get.put(AuthController());
// //   var controllerImage;

// // checkImage() {
// //   if (widget.name == "Electrical") {
// //     controllerImage = "assets/category_image/Electician.jpg";
// //   } else if (widget.name == "Plumbing") {
// //     controllerImage = "assets/category_image/Plumber.jpg";
// //   } else if (widget.name == "Masonry Works") {
// //     controllerImage = "assets/category_image/Masonry.jpg";
// //   } else if (widget.name == "Metal Works") {
// //     controllerImage = "assets/category_image/metal_works.jpg";
// //   } else if (widget.name == "Cleaning") {
// //     controllerImage = "assets/category_image/Cleaning.jpg";
// //   } else if (widget.name == "Carpentry") {
// //     controllerImage = "assets/category_image/Carpenture_1.jpg";
// //   } else if (widget.name == "Tuition and Languages") {
// //     controllerImage = "assets/category_image/Tuition _ Language classes.jpg";
// //   } else if (widget.name == "Music and Dance") {
// //     controllerImage = "assets/category_image/Music and Dance classes.jpg";
// //   } else if (widget.name == "Paint and Painting") {
// //     controllerImage = "assets/category_image/Paint _ Painting.jpg";
// //   } else if (widget.name == "Gardener and Agriculture Works") {
// //     controllerImage = "assets/category_image/Gardener.jpg";
// //   } else if (widget.name == "Healthcare and Medicine") {
// //     controllerImage = "assets/category_image/Health medicine _ Pathology.jpg";
// //   } else if (widget.name == "Veterinary and Pet Care") {
// //     controllerImage = "assets/category_image/Veterinary.jpg";
// //   } else if (widget.name == "Fitness and Yoga") {
// //     controllerImage = "assets/category_image/Fitness, yoga, med.jpg";
// //   } else if (widget.name == "Cook and Waiter") {
// //     controllerImage =
// //         "assets/category_image/Cook, Waiter, Kitchen helper.jpg";
// //   } else if (widget.name == "Home Care Staff") {
// //     controllerImage = "assets/category_image/Home care staff.jpg";
// //   } else if (widget.name == "Books and Stationery") {
// //     controllerImage = "assets/category_image/Books _ Stationery.jpg";
// //   } else if (widget.name == "Printing and Press") {
// //     controllerImage = "assets/category_image/Printing _ Press.jpg";
// //   } else if (widget.name == "Waste Management") {
// //     controllerImage = "assets/category_image/waste management.jpg";
// //   } else if (widget.name == "Catering and Rent") {
// //     controllerImage = "assets/category_image/Catering _ Rent.jpg";
// //   } else if (widget.name == "Furniture and Home Decor") {
// //     controllerImage =
// //         "assets/category_image/Furniture, Home decor _ Wallpaper.jpg";
// //   } else if (widget.name == "Vehicle") {
// //     controllerImage = "assets/category_image/Transportation.jpg";
// //   } else if (widget.name == "Travel and Tours") {
// //     controllerImage = "assets/category_image/Travel _ Tour.jpg";
// //   } else if (widget.name == "Training and Skill Program") {
// //     controllerImage = "assets/category_image/Training _ skill program.jpg";
// //   } else if (widget.name == "Event and Party") {
// //     controllerImage = "assets/category_image/Event _ Party Planner.jpg";
// //   } else if (widget.name == "Engineering") {
// //     controllerImage = "assets/category_image/Engineering.jpg";
// //   } else if (widget.name == "Office Staff") {
// //     controllerImage = "assets/category_image/Office Staff.jpg";
// //   } else if (widget.name == "Advertisement and Promotion") {
// //     controllerImage = "assets/category_image/Advertisement _ Promotion_3.jpg";
// //   } else if (widget.name == "Dharmik Karyakram") {
// //     controllerImage = "assets/category_image/dharmik.jpg";
// //   } else if (widget.name == "Many More") {
// //     controllerImage = "assets/category_image/Others.jpg";
// //   }
// // }

// //   @override
// //   void initState() {
// //     checkImage();
// //     super.initState();
// //   }

// //   @override
// //   Widget build(BuildContext context) {
// //     Size size = MediaQuery.of(context).size;
// //     return Scaffold(
// //       appBar: myAppbar(context, true, widget.name),
// //       body: Container(
// //         // margin: const EdgeInsets.only(top: 18),
// //         width: double.infinity,
// //         decoration: const BoxDecoration(
// //             // borderRadius: BorderRadius.only(
// //             //   topRight: Radius.circular(30),
// //             //   topLeft: Radius.circular(30),
// //             // ),
// //             color: Colors.white),
// //         child: Column(
// //           children: [
// //             controllerImage == null
// //                 ? Image.asset('assets/Logo.png')
// //                 : Image.asset(controllerImage),
// //             // const Divider(
// //             //   color: Colors.black,
// //             //   thickness: 0.6,
// //             // ),
// //             Expanded(
// //               child: Padding(
// //                 padding: const EdgeInsets.all(12.0),
// //                 child: FutureBuilder(
// //                     future: fetchServices(),
// //                     builder: (context, snapshot) {
// //                       if (snapshot.hasData) {
// //                         return ListView.builder(
// //                           itemCount: snapshot.data!.length,
// //                           itemBuilder: (context, index) {
// //                             return InkWell(
// //                               onTap: () {
// //                                 sid = snapshot.data?[index].id.toString();
// //                                 consolelog("pp map $sid");
// //                                 controller.pickedJobFieldName.value =
// //                                     snapshot.data![index].id.toString();
// //                                 Get.to(() => MapScreen(
// //                                       work: snapshot.data![index].category
// //                                               ?.categoryTitle ??
// //                                           "",
// //                                       name: snapshot.data?[index].name ?? "",
// //                                     ));

// //                                 // MapScreen(jobName:
// //                                 //       work: snapshot
// //                                 //           .data![index].category.categoryTitle,
// //                                 //     ));
// //                               },
// //                               child: Card(
// //                                 margin: const EdgeInsets.all(8),
// //                                 shape: RoundedRectangleBorder(
// //                                     borderRadius: BorderRadius.circular(12),
// //                                     side: BorderSide(
// //                                         color: Theme.of(context).primaryColor)),
// //                                 child: Padding(
// //                                   padding: const EdgeInsets.symmetric(
// //                                       vertical: 20, horizontal: 10),
// //                                   child: Text(
// //                                     snapshot.data?[index].name ?? "",
// //                                     style: const TextStyle(
// //                                       letterSpacing: 0.51,
// //                                       fontFamily: 'hello',
// //                                       fontSize: 16,
// //                                       fontWeight: FontWeight.w500,
// //                                       color: Colors.black,
// //                                     ),
// //                                   ),
// //                                 ),
// //                               ),
// //                             );
// //                           },
// //                         );
// //                       }
// //                       if (snapshot.hasError) {
// //                         return Text(snapshot.error.toString());
// //                       } else {
// //                         return const Center(
// //                           child: CircularProgressIndicator(),
// //                         );
// //                       }
// //                     }),
// //               ),
// //             )
// //           ],
// //         ),
// //       ),
// //     );
// //   }

// //   String baseUrl = BaseClient().baseUrl;

// //   Future<List<ServicesList>> fetchServices() async {
// //     try {
// //       SharedPreferences prefs = await SharedPreferences.getInstance();
// //       String? apptoken = prefs.getString("token");
// //       int? id = prefs.getInt("id");
// //       log(id.toString());
// //       log('Get services init $apptoken');
// //       // log('get service ${controller.token}');

// //       consolelog('widget${widget.id}');

// //       final res = await http.get(
// //         Uri.parse('$baseUrl/api/category/${widget.id}/services'),
// //         headers: <String, String>{
// //           'Content-Type': 'application/json',
// //           'Authorization': "Bearer $apptoken"
// //         },
// //       ).timeout(const Duration(seconds: 20));

// //       if (res.statusCode == 200) {
// //         consolelog(res.body);
// //         List myList = jsonDecode(res.body);
// //         return myList.map((e) => ServicesList.fromJson(e)).toList();
// //       } else {
// //         errorToast(msg: "Could not fetch data from server");
// //         Get.to(() => const MainScreen());
// //         return []; // Return an empty list in case of an error
// //       }
// //     } on TimeoutException catch (e) {
// //       errorToast(msg: "Request timed out. Please try again.");

// //       log('TimeoutException: $e');
// //       return []; // Return an empty list in case of a timeout
// //     } catch (e) {
// //       errorToast(msg: "An unexpected error occurred.");

// //       log('Exception: $e');
// //       return []; // Return an empty list in case of any other errors
// //     }
// //   }
// // }

// import 'dart:async';
// import 'dart:convert';
// import 'dart:developer';
// import 'package:flutter/material.dart';
// import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
// import 'package:get/get.dart';
// import 'package:http/http.dart' as http;
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:smartsewa/core/development/console.dart';
// import 'package:smartsewa/network/base_client.dart';
// import 'package:smartsewa/network/services/authServices/auth_controller.dart';
// import 'package:smartsewa/views/user_screen/drawer%20screen/map_Screen.dart';
// import 'package:smartsewa/views/widgets/custom_toasts.dart';
// import '../../../network/models/servicemodel.dart';
// import '../../widgets/my_appbar.dart';

// class ServicesScreen extends StatefulWidget {
//   const ServicesScreen({
//     super.key,
//     required this.id,
//     required this.name,
//   });
//   final String id;
//   final String name;

//   @override
//   State<ServicesScreen> createState() => _ServicesScreenState();
// }

// class _ServicesScreenState extends State<ServicesScreen>
//     with TickerProviderStateMixin {
//   var sid;
//   final controller = Get.put(AuthController());
//   var controllerImage;

//   // Animation Controllers
//   late AnimationController _imageController;
//   late AnimationController _loadingController;
//   late AnimationController _tapController;

//   // Check and set the image based on the service name
//   void checkImage() {
//     Map<String, String> imageMap = {
//       "Electrical": "assets/category_image/Electician.jpg",
//       "Plumbing": "assets/category_image/Plumber.jpg",
//       "Masonry Works": "assets/category_image/Masonry.jpg",
//       "Metal Works": "assets/category_image/metal_works.jpg",
//       "Cleaning": "assets/category_image/Cleaning.jpg",
//       "Carpentry": "assets/category_image/Carpenture_1.jpg",
//       "Tuition and Languages":
//           "assets/category_image/Tuition _ Language classes.jpg",
//       "Music and Dance": "assets/category_image/Music and Dance classes.jpg",
//       "Paint and Painting": "assets/category_image/Paint _ Painting.jpg",
//       "Gardener and Agriculture Works": "assets/category_image/Gardener.jpg",
//       "Healthcare and Medicine":
//           "assets/category_image/Health medicine _ Pathology.jpg",
//       "Veterinary and Pet Care": "assets/category_image/Veterinary.jpg",
//       "Fitness and Yoga": "assets/category_image/Fitness, yoga, med.jpg",
//       "Cook and Waiter":
//           "assets/category_image/Cook, Waiter, Kitchen helper.jpg",
//       "Home Care Staff": "assets/category_image/Home care staff.jpg",
//       "Books and Stationery": "assets/category_image/Books _ Stationery.jpg",
//       "Printing and Press": "assets/category_image/Printing _ Press.jpg",
//       "Waste Management": "assets/category_image/waste management.jpg",
//       "Catering and Rent": "assets/category_image/Catering _ Rent.jpg",
//       "Furniture and Home Decor":
//           "assets/category_image/Furniture, Home decor _ Wallpaper.jpg",
//       "Vehicle": "assets/category_image/Transportation.jpg",
//       "Travel and Tours": "assets/category_image/Travel _ Tour.jpg",
//       "Training and Skill Program":
//           "assets/category_image/Training _ skill program.jpg",
//       "Event and Party": "assets/category_image/Event _ Party Planner.jpg",
//       "Engineering": "assets/category_image/Engineering.jpg",
//       "Office Staff": "assets/category_image/Office Staff.jpg",
//       "Advertisement and Promotion":
//           "assets/category_image/Advertisement _ Promotion_3.jpg",
//       "Dharmik Karyakram": "assets/category_image/dharmik.jpg",
//       "Many More": "assets/category_image/Others.jpg",
//     };

//     controllerImage =
//         imageMap[widget.name] ?? 'assets/category_image/Others.jpg';
//   }

//   @override
//   void initState() {
//     super.initState();
//     checkImage();

//     // Initialize animation controllers
//     _imageController = AnimationController(
//       vsync: this,
//       duration: const Duration(milliseconds: 800),
//     );

//     _loadingController = AnimationController(
//       vsync: this,
//       duration: const Duration(milliseconds: 500),
//     )..repeat(reverse: true);

//     _tapController = AnimationController(
//       vsync: this,
//       duration: const Duration(milliseconds: 200),
//     );

//     // Start image animation after a slight delay
//     Future.delayed(const Duration(milliseconds: 300), () {
//       _imageController.forward();
//     });
//   }

//   @override
//   void dispose() {
//     // Dispose animation controllers to avoid memory leaks
//     _imageController.dispose();
//     _loadingController.dispose();
//     _tapController.dispose();
//     super.dispose();
//   }

//   @override
//   Widget build(BuildContext context) {
//     Size size = MediaQuery.of(context).size;
//     return Scaffold(
//       appBar: myAppbar(context, true, widget.name),
//       body: Container(
//         width: double.infinity,
//         decoration: const BoxDecoration(
//           color: Color(0xFFF9F9F9),
//         ),
//         child: Column(
//           children: [
//             // Animated Image Container
//             if (controllerImage != null)
//               AnimatedBuilder(
//                 animation: _imageController,
//                 builder: (context, child) {
//                   return Opacity(
//                     opacity: _imageController.value,
//                     child: Transform.translate(
//                       offset: Offset(0.0, (1 - _imageController.value) * 20),
//                       child: child,
//                     ),
//                   );
//                 },
//                 child: Container(
//                   margin: const EdgeInsets.all(16.0),
//                   child: ClipRRect(
//                     borderRadius: BorderRadius.circular(16.0),
//                     child: Image.asset(
//                       controllerImage,
//                       fit: BoxFit.cover,
//                       height: 140,
//                       width: size.width - 12,
//                     ),
//                   ),
//                 ),
//               ),

//             // Services List
//             Expanded(
//               child: Padding(
//                 padding: const EdgeInsets.symmetric(horizontal: 16.0),
//                 child: FutureBuilder(
//                   future: fetchServices(),
//                   builder: (context, snapshot) {
//                     if (snapshot.connectionState == ConnectionState.waiting) {
//                       // Loading Animation
//                       return Center(
//                         child: AnimatedBuilder(
//                           animation: _loadingController,
//                           builder: (context, child) {
//                             return Transform.translate(
//                               offset: Offset(0, -_loadingController.value * 10),
//                               child: Icon(
//                                 Icons.home_repair_service,
//                                 size: 40,
//                                 color: Theme.of(context).primaryColor,
//                               ),
//                             );
//                           },
//                         ),
//                       );
//                     } else if (snapshot.hasError) {
//                       return Center(
//                         child: Text(
//                           "Error: ${snapshot.error}",
//                           style: TextStyle(color: Colors.redAccent),
//                         ),
//                       );
//                     } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
//                       return const Center(
//                         child: Text(
//                           "No services available",
//                           style: TextStyle(
//                             fontSize: 18,
//                             color: Colors.grey,
//                           ),
//                         ),
//                       );
//                     } else {
//                       // Animated List of Services
//                       return AnimationLimiter(
//                         child: ListView.builder(
//                           itemCount: snapshot.data!.length,
//                           itemBuilder: (context, index) {
//                             return AnimationConfiguration.staggeredList(
//                               position: index,
//                               duration: const Duration(milliseconds: 500),
//                               child: SlideAnimation(
//                                 verticalOffset: 50.0,
//                                 child: FadeInAnimation(
//                                   child: ScaleTransition(
//                                     scale: _tapController.drive(
//                                       Tween(begin: 1.0, end: 0.95),
//                                     ),
//                                     child: Card(
//                                       margin: const EdgeInsets.symmetric(
//                                           vertical: 8.0),
//                                       shape: RoundedRectangleBorder(
//                                         borderRadius:
//                                             BorderRadius.circular(16.0),
//                                       ),
//                                       elevation: 6,
//                                       color: Colors.white,
//                                       child: InkWell(
//                                         onTap: () {
//                                           // Tap animation
//                                           _tapController.forward().then((_) {
//                                             _tapController.reverse();
//                                           });

//                                           sid = snapshot.data?[index].id
//                                               .toString();
//                                           controller.pickedJobFieldName.value =
//                                               snapshot.data![index].id
//                                                   .toString();
//                                           Get.to(() => MapScreen(
//                                                 work: snapshot
//                                                         .data![index]
//                                                         .category
//                                                         ?.categoryTitle ??
//                                                     "",
//                                                 name: snapshot
//                                                         .data?[index].name ??
//                                                     "",
//                                               ));
//                                         },
//                                         child: Padding(
//                                           padding: const EdgeInsets.symmetric(
//                                               vertical: 20, horizontal: 16),
//                                           child: Row(
//                                             children: [
//                                               Container(
//                                                 padding:
//                                                     const EdgeInsets.all(12.0),
//                                                 decoration: BoxDecoration(
//                                                   color: Theme.of(context)
//                                                       .primaryColor
//                                                       .withOpacity(0.1),
//                                                   borderRadius:
//                                                       BorderRadius.circular(
//                                                           12.0),
//                                                 ),
//                                                 child: Icon(
//                                                   Icons.home_repair_service,
//                                                   color: Theme.of(context)
//                                                       .primaryColor,
//                                                   size: 30,
//                                                 ),
//                                               ),
//                                               const SizedBox(width: 16),
//                                               Expanded(
//                                                 child: Column(
//                                                   crossAxisAlignment:
//                                                       CrossAxisAlignment.start,
//                                                   children: [
//                                                     Text(
//                                                       snapshot.data?[index]
//                                                               .name ??
//                                                           "",
//                                                       style: const TextStyle(
//                                                         fontSize: 18,
//                                                         fontWeight:
//                                                             FontWeight.w600,
//                                                         color: Colors.black87,
//                                                       ),
//                                                     ),
//                                                     if (snapshot
//                                                             .data?[index]
//                                                             .category
//                                                             ?.categoryTitle !=
//                                                         null)
//                                                       Text(
//                                                         snapshot
//                                                             .data![index]
//                                                             .category!
//                                                             .categoryTitle!,
//                                                         style: const TextStyle(
//                                                           fontSize: 14,
//                                                           color: Colors.grey,
//                                                         ),
//                                                       ),
//                                                   ],
//                                                 ),
//                                               ),
//                                               const Icon(
//                                                 Icons.arrow_forward_ios,
//                                                 color: Colors.grey,
//                                                 size: 18,
//                                               ),
//                                             ],
//                                           ),
//                                         ),
//                                       ),
//                                     ),
//                                   ),
//                                 ),
//                               ),
//                             );
//                           },
//                         ),
//                       );
//                     }
//                   },
//                 ),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   // Fetch services from the API
//   Future<List<ServicesList>> fetchServices() async {
//     try {
//       SharedPreferences prefs = await SharedPreferences.getInstance();
//       String? apptoken = prefs.getString("token");
//       log('Fetching services with token: $apptoken');

//       final res = await http.get(
//         Uri.parse('${BaseClient().baseUrl}/api/category/${widget.id}/services'),
//         headers: {
//           'Content-Type': 'application/json',
//           'Authorization': "Bearer $apptoken",
//         },
//       ).timeout(const Duration(seconds: 20));

//       if (res.statusCode == 200) {
//         List myList = jsonDecode(res.body);
//         return myList.map((e) => ServicesList.fromJson(e)).toList();
//       } else {
//         throw Exception("Failed to fetch data");
//       }
//     } catch (e) {
//       log('Error fetching services: $e');
//       throw Exception("Error: $e");
//     }
//   }
// }

import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/network/base_client.dart';
import 'package:smartsewa/network/services/authServices/auth_controller.dart';
import 'package:smartsewa/views/user_screen/drawer%20screen/map_Screen.dart';
import 'package:smartsewa/views/widgets/custom_toasts.dart';
import '../../../network/models/servicemodel.dart';
import '../../widgets/my_appbar.dart';

// Enhanced API Response Model
class ApiResponse<T> {
  final bool success;
  final T? data;
  final String? message;
  final int? statusCode;
  final String? error;

  ApiResponse({
    required this.success,
    this.data,
    this.message,
    this.statusCode,
    this.error,
  });

  factory ApiResponse.success(
      {required T data, String? message, int? statusCode}) {
    return ApiResponse(
      success: true,
      data: data,
      message: message,
      statusCode: statusCode,
    );
  }

  factory ApiResponse.error({required String error, int? statusCode}) {
    return ApiResponse(
      success: false,
      error: error,
      statusCode: statusCode,
    );
  }
}

class ServicesScreen extends StatefulWidget {
  const ServicesScreen({
    super.key,
    required this.id,
    required this.name,
  });
  final String id;
  final String name;

  @override
  State<ServicesScreen> createState() => _ServicesScreenState();
}

class _ServicesScreenState extends State<ServicesScreen>
    with TickerProviderStateMixin {
  var sid;
  final controller = Get.put(AuthController());
  var controllerImage;

  // Animation Controllers
  late AnimationController _imageController;
  late AnimationController _loadingController;
  late AnimationController _tapController;

  // Add these for better state management
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  List<ServicesList>? _cachedServices;

  // Check and set the image based on the service name
  void checkImage() {
    Map<String, String> imageMap = {
      "Electrical": "assets/category_image/Electician.jpg",
      "Plumbing": "assets/category_image/Plumber.jpg",
      "Masonry Works": "assets/category_image/Masonry.jpg",
      "Metal Works": "assets/category_image/metal_works.jpg",
      "Cleaning": "assets/category_image/Cleaning.jpg",
      "Carpentry": "assets/category_image/Carpenture_1.jpg",
      "Tuition and Languages":
          "assets/category_image/Tuition _ Language classes.jpg",
      "Music and Dance": "assets/category_image/Music and Dance classes.jpg",
      "Paint and Painting": "assets/category_image/Paint _ Painting.jpg",
      "Gardener and Agriculture Works": "assets/category_image/Gardener.jpg",
      "Healthcare and Medicine":
          "assets/category_image/Health medicine _ Pathology.jpg",
      "Veterinary and Pet Care": "assets/category_image/Veterinary.jpg",
      "Fitness and Yoga": "assets/category_image/Fitness, yoga, med.jpg",
      "Cook and Waiter":
          "assets/category_image/Cook, Waiter, Kitchen helper.jpg",
      "Home Care Staff": "assets/category_image/Home care staff.jpg",
      "Books and Stationery": "assets/category_image/Books _ Stationery.jpg",
      "Printing and Press": "assets/category_image/Printing _ Press.jpg",
      "Waste Management": "assets/category_image/waste management.jpg",
      "Catering and Rent": "assets/category_image/Catering _ Rent.jpg",
      "Furniture and Home Decor":
          "assets/category_image/Furniture, Home decor _ Wallpaper.jpg",
      "Vehicle": "assets/category_image/Transportation.jpg",
      "Travel and Tours": "assets/category_image/Travel _ Tour.jpg",
      "Training and Skill Program":
          "assets/category_image/Training _ skill program.jpg",
      "Event and Party": "assets/category_image/Event _ Party Planner.jpg",
      "Engineering": "assets/category_image/Engineering.jpg",
      "Office Staff": "assets/category_image/Office Staff.jpg",
      "Advertisement and Promotion":
          "assets/category_image/Advertisement _ Promotion_3.jpg",
      "Dharmik Karyakram": "assets/category_image/dharmik.jpg",
      "Many More": "assets/category_image/Others.jpg",
    };

    controllerImage =
        imageMap[widget.name] ?? 'assets/category_image/Others.jpg';
  }

  @override
  void initState() {
    super.initState();
    checkImage();

    // Initialize animation controllers
    _imageController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
    );

    _loadingController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    )..repeat(reverse: true);

    _tapController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    // Start image animation after a slight delay
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        _imageController.forward();
      }
    });
  }

  @override
  void dispose() {
    // Dispose animation controllers to avoid memory leaks
    _imageController.dispose();
    _loadingController.dispose();
    _tapController.dispose();
    super.dispose();
  }

  // Enhanced retry mechanism
  Future<void> _retryFetch() async {
    setState(() {
      _hasError = false;
      _errorMessage = '';
    });

    // Clear cached data to force refresh
    _cachedServices = null;

    // Trigger rebuild with FutureBuilder
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return Scaffold(
      appBar: myAppbar(context, true, widget.name),
      body: Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          color: Color(0xFFF9F9F9),
        ),
        child: Column(
          children: [
            // Animated Image Container
            if (controllerImage != null)
              AnimatedBuilder(
                animation: _imageController,
                builder: (context, child) {
                  return Opacity(
                    opacity: _imageController.value,
                    child: Transform.translate(
                      offset: Offset(0.0, (1 - _imageController.value) * 20),
                      child: child,
                    ),
                  );
                },
                child: Container(
                  margin: const EdgeInsets.all(16.0),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(16.0),
                    child: Image.asset(
                      controllerImage,
                      fit: BoxFit.cover,
                      height: 140,
                      width: size.width - 12,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          height: 140,
                          width: size.width - 12,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(16.0),
                          ),
                          child: const Icon(
                            Icons.image_not_supported,
                            size: 50,
                            color: Colors.grey,
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ),

            // Services List
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: FutureBuilder<ApiResponse<List<ServicesList>>>(
                  future: fetchServices(),
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting) {
                      // Enhanced Loading Animation
                      return Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            AnimatedBuilder(
                              animation: _loadingController,
                              builder: (context, child) {
                                return Transform.translate(
                                  offset:
                                      Offset(0, -_loadingController.value * 10),
                                  child: Icon(
                                    Icons.home_repair_service,
                                    size: 40,
                                    color: Theme.of(context).primaryColor,
                                  ),
                                );
                              },
                            ),
                            const SizedBox(height: 16),
                            Text(
                              "Loading services...",
                              style: TextStyle(
                                color: Theme.of(context).primaryColor,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                      );
                    }

                    // Handle different response states
                    if (snapshot.hasError) {
                      return _buildErrorWidget("Network error occurred");
                    }

                    if (!snapshot.hasData) {
                      return _buildErrorWidget("No response received");
                    }

                    final response = snapshot.data!;

                    if (!response.success) {
                      return _buildErrorWidget(
                          response.error ?? "Failed to load services");
                    }

                    final services = response.data;

                    if (services == null || services.isEmpty) {
                      return _buildEmptyState();
                    }

                    // Success - Display Services
                    return _buildServicesList(services);
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Build error widget with retry option
  Widget _buildErrorWidget(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 60,
            color: Colors.redAccent,
          ),
          const SizedBox(height: 16),
          Text(
            "Oops! Something went wrong",
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20),
          ElevatedButton.icon(
            onPressed: _retryFetch,
            icon: const Icon(
              Icons.refresh,
              color: Colors.white,
            ),
            label: const Text("Try Again"),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  // Build empty state widget
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 60,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          const Text(
            "No services available",
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            "We couldn't find any services in this category.",
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 20),
          ElevatedButton.icon(
            onPressed: _retryFetch,
            icon: const Icon(Icons.refresh),
            label: const Text("Refresh"),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  // Build services list widget
  Widget _buildServicesList(List<ServicesList> services) {
    return AnimationLimiter(
      child: ListView.builder(
        itemCount: services.length,
        itemBuilder: (context, index) {
          final service = services[index];
          return AnimationConfiguration.staggeredList(
            position: index,
            duration: const Duration(milliseconds: 500),
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: Card(
                  margin: const EdgeInsets.symmetric(vertical: 8.0),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16.0),
                  ),
                  elevation: 6,
                  color: Colors.white,
                  child: InkWell(
                    onTap: () => _handleServiceTap(service),
                    borderRadius: BorderRadius.circular(16.0),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 20, horizontal: 16),
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(12.0),
                            decoration: BoxDecoration(
                              color: Theme.of(context)
                                  .primaryColor
                                  .withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12.0),
                            ),
                            child: Icon(
                              Icons.home_repair_service,
                              color: Theme.of(context).primaryColor,
                              size: 30,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  service.name ?? "Unnamed Service",
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black87,
                                  ),
                                ),
                                if (service.category?.categoryTitle != null)
                                  Text(
                                    service.category!.categoryTitle!,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: Colors.grey,
                                    ),
                                  ),
                              ],
                            ),
                          ),
                          const Icon(
                            Icons.arrow_forward_ios,
                            color: Colors.grey,
                            size: 18,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // Handle service tap with animation
  void _handleServiceTap(ServicesList service) {
    _tapController.forward().then((_) {
      _tapController.reverse();
    });

    try {
      sid = service.id?.toString();
      controller.pickedJobFieldName.value = service.id?.toString() ?? '';

      Get.to(() => MapScreen(
            work: service.category?.categoryTitle ?? "",
            name: service.name ?? "",
          ));
    } catch (e) {
      log('Error navigating to map screen: $e');
      // Show error toast
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Unable to open service details'),
          backgroundColor: Colors.redAccent,
        ),
      );
    }
  }

  // Enhanced API call with proper response handling
  Future<ApiResponse<List<ServicesList>>> fetchServices() async {
    // Return cached data if available
    if (_cachedServices != null) {
      return ApiResponse.success(data: _cachedServices!);
    }

    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? apptoken = prefs.getString("token");

      if (apptoken == null || apptoken.isEmpty) {
        return ApiResponse.error(
          error: "Authentication token not found. Please login again.",
          statusCode: 401,
        );
      }

      log('Fetching services for category: ${widget.id} with token: ${apptoken.substring(0, 10)}...');

      final response = await http.get(
        Uri.parse('${BaseClient().baseUrl}/api/category/${widget.id}/services'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': "Bearer $apptoken",
          'Accept': 'application/json',
        },
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          throw TimeoutException(
              'Request timeout', const Duration(seconds: 30));
        },
      );

      log('API Response Status: ${response.statusCode}');
      log('API Response Body: ${response.body}');

      // Handle different status codes
      switch (response.statusCode) {
        case 200:
          try {
            final dynamic jsonData = jsonDecode(response.body);

            // Handle different response structures
            List<dynamic> servicesList;
            if (jsonData is Map<String, dynamic>) {
              // If response is wrapped in an object
              if (jsonData.containsKey('data')) {
                servicesList = jsonData['data'] as List<dynamic>;
              } else if (jsonData.containsKey('services')) {
                servicesList = jsonData['services'] as List<dynamic>;
              } else {
                return ApiResponse.error(
                  error: "Unexpected response format",
                  statusCode: response.statusCode,
                );
              }
            } else if (jsonData is List<dynamic>) {
              // If response is directly a list
              servicesList = jsonData;
            } else {
              return ApiResponse.error(
                error: "Invalid response format",
                statusCode: response.statusCode,
              );
            }

            final services = servicesList
                .map((e) => ServicesList.fromJson(e as Map<String, dynamic>))
                .toList();

            // Cache the successful response
            _cachedServices = services;

            return ApiResponse.success(
              data: services,
              statusCode: response.statusCode,
              message: "Services loaded successfully",
            );
          } catch (e) {
            log('JSON parsing error: $e');
            return ApiResponse.error(
              error: "Failed to parse response data",
              statusCode: response.statusCode,
            );
          }

        case 401:
          return ApiResponse.error(
            error: "Unauthorized. Please login again.",
            statusCode: response.statusCode,
          );

        case 403:
          return ApiResponse.error(
            error:
                "Access forbidden. You don't have permission to view these services.",
            statusCode: response.statusCode,
          );

        case 404:
          return ApiResponse.error(
            error: "Category not found or no services available.",
            statusCode: response.statusCode,
          );

        case 429:
          return ApiResponse.error(
            error: "Too many requests. Please try again later.",
            statusCode: response.statusCode,
          );

        case 500:
        case 502:
        case 503:
          return ApiResponse.error(
            error: "Server error. Please try again later.",
            statusCode: response.statusCode,
          );

        default:
          return ApiResponse.error(
            error: "Unexpected error occurred (${response.statusCode})",
            statusCode: response.statusCode,
          );
      }
    } on SocketException {
      return ApiResponse.error(
        error: "No internet connection. Please check your network.",
      );
    } on TimeoutException {
      return ApiResponse.error(
        error: "Request timeout. Please try again.",
      );
    } on FormatException catch (e) {
      log('Format exception: $e');
      return ApiResponse.error(
        error: "Invalid data format received from server.",
      );
    } catch (e) {
      log('Unexpected error: $e');
      return ApiResponse.error(
        error: "An unexpected error occurred: ${e.toString()}",
      );
    }
  }
}
