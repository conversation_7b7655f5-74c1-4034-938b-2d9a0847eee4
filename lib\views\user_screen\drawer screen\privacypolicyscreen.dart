// import 'package:flutter/material.dart';
// import 'package:smartsewa/views/widgets/my_appbar.dart';

// class PrivacypolicyScreen extends StatelessWidget {
//   const PrivacypolicyScreen({super.key});

//   @override
//   Widget build(BuildContext context) {
//     Size size = MediaQuery.of(context).size;
//     return Scaffold(
//       appBar: myAppbar(context, true, "Terms & Condition"),
//       backgroundColor: Colors.white,
//       body: SafeArea(
//         child: Padding(
//           padding: const EdgeInsets.all(18),
//           child: SingleChildScrollView(
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 // app name
//                 const Text(
//                   'Smart Sewa Solutions Nepal Pvt. Ltd',
//                   textAlign: TextAlign.center,
//                   style: TextStyle(
//                       fontSize: 22,
//                       color: Color.fromARGB(240, 0, 131, 143),
//                       fontWeight: FontWeight.bold),
//                 ),

//                 SizedBox(height: size.height * 0.02),

//                 // description
//                 const Text(
//                   'Please read the following disclaimer carefully before using Smart Sewa App operated by Smart Sewa '
//                   'Solutions Nepal Pvt. Ltd. Your access and use of the Service are subject to your acceptance and '
//                   'compliance with these Terms. These Terms apply to all users, visitors, and anyone else who uses or '
//                   'accesses the service. If you do not agree to any part of these terms, you may not access or use the '
//                   'service. Please note that these Terms of Service serve as a general agreement for accessing and using the '
//                   'Smart Sewa app and services. ',
//                   textAlign: TextAlign.justify,
//                   style: TextStyle(
//                     fontSize: 18,
//                   ),
//                 ),

//                 SizedBox(height: size.height * 0.02),

//                 const Text(
//                   'Disclaimer',
//                   style: TextStyle(
//                     fontSize: 22,
//                     fontWeight: FontWeight.bold,
//                     color: Color.fromRGBO(0, 131, 143, 1),
//                   ),
//                 ),
//                 SizedBox(height: size.height * 0.015),

//                 // disclaimer
//                 const Text(
//                   'Our mobile application acts as a third party between service providers and service receivers. We do not'
//                   'provide any services ourselves and only act as a mediator to connect service providers and service '
//                   'receivers. We do not endorse any service provider or service receiver and do not guarantee the quality '
//                   'or safety of any services provided. However, we will handle the compliance and allow to rate the genuine '
//                   'service providers. In case of misconduct, we can disqualify any user for providing service via Smart Sewa '
//                   'application which is sole right of our company. '
//                   'By using our mobile application, you acknowledge and agree that we are not responsible for any '
//                   'interactions or transactions between service providers and service receivers. Any financial disputes or '
//                   'other issues that may arise between service providers and service receivers are solely between those '
//                   'parties and are not the responsibility as well as liability of our company. ',
//                   textAlign: TextAlign.justify,
//                   style: TextStyle(
//                     fontSize: 18,
//                   ),
//                 ),
//                 SizedBox(height: size.height * 0.015),

//                 // disclaimer
//                 const Text(
//                   'We are not liable for any loss or damage that may occur as a result of using our mobile application or any '
//                   'services provided by service providers. Our company is not responsible for any legal or financial liability '
//                   'that may arise from interactions or transactions between service providers and service receivers. '
//                   'By using our mobile application, you agree to release our company from any and all claims, damages, or '
//                   'disputes that may arise from interactions or transactions between service providers and service '
//                   'receivers. You agree to use our mobile application at your own risk and assume all responsibility for any '
//                   'consequences that may arise. '
//                   'In case of disputes, depending on its nature the governing rules and laws of Nepal will prevail to settle '
//                   'the disputes. ',
//                   textAlign: TextAlign.justify,
//                   style: TextStyle(
//                     fontSize: 18,
//                   ),
//                 ),
//                 SizedBox(height: size.height * 0.015),
//                 // AppButton(
//                 // name: "Back",
//                 //  onPressed: () {
//                 //    Get.back();
//                 // }),
//                 SizedBox(height: size.height * 0.02),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }

// import 'package:flutter/material.dart';
// import 'package:smartsewa/views/widgets/my_appbar.dart';

// class PrivacypolicyScreen extends StatelessWidget {
//   const PrivacypolicyScreen({super.key});

//   @override
//   Widget build(BuildContext context) {
//     Size size = MediaQuery.of(context).size;
//     return Scaffold(
//       appBar: myAppbar(context, true, "Terms & Condition"),
//       backgroundColor: Colors.white,
//       body: SafeArea(
//         child: Padding(
//           padding: const EdgeInsets.all(18),
//           child: SingleChildScrollView(
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               children: [
//                 // App name
//                 const Text(
//                   'Smart Sewa Solutions Nepal Pvt. Ltd',
//                   textAlign: TextAlign.center,
//                   style: TextStyle(
//                       fontSize: 22,
//                       color: Color.fromARGB(240, 0, 131, 143),
//                       fontWeight: FontWeight.bold),
//                 ),
//                 SizedBox(height: size.height * 0.02),

//                 // Description
//                 const Text(
//                   'Please read the following disclaimer carefully before using Smart Sewa App operated by Smart Sewa '
//                   'Solutions Nepal Pvt. Ltd. Your access and use of the Service are subject to your acceptance and '
//                   'compliance with these Terms. These Terms apply to all users, visitors, and anyone else who uses or '
//                   'accesses the service. If you do not agree to any part of these terms, you may not access or use the '
//                   'service. Please note that these Terms of Service serve as a general agreement for accessing and using the '
//                   'Smart Sewa app and services.',
//                   textAlign: TextAlign.justify,
//                   style: TextStyle(
//                     fontSize: 18,
//                     height: 1.5, // Improve line spacing for readability
//                   ),
//                 ),
//                 SizedBox(height: size.height * 0.03),

//                 // Disclaimer header
//                 const Text(
//                   'Disclaimer',
//                   style: TextStyle(
//                     fontSize: 22,
//                     fontWeight: FontWeight.bold,
//                     color: Color.fromRGBO(0, 131, 143, 1),
//                   ),
//                 ),
//                 SizedBox(height: size.height * 0.015),

//                 // Disclaimer Text
//                 const Text(
//                   'Our mobile application acts as a third party between service providers and service receivers. We do not '
//                   'provide any services ourselves and only act as a mediator to connect service providers and service '
//                   'receivers. We do not endorse any service provider or service receiver and do not guarantee the quality '
//                   'or safety of any services provided. However, we will handle the compliance and allow to rate the genuine '
//                   'service providers. In case of misconduct, we can disqualify any user for providing service via Smart Sewa '
//                   'application which is sole right of our company. '
//                   'By using our mobile application, you acknowledge and agree that we are not responsible for any '
//                   'interactions or transactions between service providers and service receivers. Any financial disputes or '
//                   'other issues that may arise between service providers and service receivers are solely between those '
//                   'parties and are not the responsibility as well as liability of our company.',
//                   textAlign: TextAlign.justify,
//                   style: TextStyle(
//                     fontSize: 18,
//                     height: 1.5, // Improve line spacing for readability
//                   ),
//                 ),
//                 SizedBox(height: size.height * 0.03),

//                 // Additional Disclaimer Text
//                 const Text(
//                   'We are not liable for any loss or damage that may occur as a result of using our mobile application or any '
//                   'services provided by service providers. Our company is not responsible for any legal or financial liability '
//                   'that may arise from interactions or transactions between service providers and service receivers. '
//                   'By using our mobile application, you agree to release our company from any and all claims, damages, or '
//                   'disputes that may arise from interactions or transactions between service providers and service '
//                   'receivers. You agree to use our mobile application at your own risk and assume all responsibility for any '
//                   'consequences that may arise. '
//                   'In case of disputes, depending on its nature the governing rules and laws of Nepal will prevail to settle '
//                   'the disputes.',
//                   textAlign: TextAlign.justify,
//                   style: TextStyle(
//                     fontSize: 18,
//                     height: 1.5, // Improve line spacing for readability
//                   ),
//                 ),
//                 SizedBox(
//                     height: size.height * 0.03), // Extra space at the bottom
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
// }

// import 'package:flutter/material.dart';
// import 'package:smartsewa/views/widgets/my_appbar.dart';

// class PrivacypolicyScreen extends StatelessWidget {
//   const PrivacypolicyScreen({super.key});

//   void handleAccept(BuildContext context) {
//     showDialog(
//       context: context,
//       builder: (context) => AlertDialog(
//         title: const Text('Accepted'),
//         content: const Text('You have accepted the terms and conditions.'),
//         actions: [
//           TextButton(
//             onPressed: () {
//               Navigator.pop(context); // Close dialog
//               Navigator.pushReplacementNamed(
//                   context, '/home'); // Navigate to the home screen
//             },
//             child: const Text('OK'),
//           ),
//         ],
//       ),
//     );
//   }

//   void handleDecline(BuildContext context) {
//     Navigator.pop(context);
//   }

//   @override
//   Widget build(BuildContext context) {
//     Size size = MediaQuery.of(context).size;
//     return Scaffold(
//         appBar: myAppbar(context, true, "Terms & Conditions"),
//         backgroundColor: Colors.white,
//         body: const SafeArea(
//           child: Padding(
//             padding: EdgeInsets.all(20),
//             child: SingleChildScrollView(
//               child: Card(
//                 color: Color.fromARGB(255, 255, 255, 255),
//                 margin: EdgeInsets.all(10),
//                 elevation: 15,
//                 child: Padding(
//                   padding: EdgeInsets.all(20),
//                   child: Column(
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       // Section 1: Company Name
//                       Text(
//                         'Smart Sewa Solutions Nepal Pvt. Ltd',
//                         textAlign: TextAlign.center,
//                         style: TextStyle(
//                           fontSize: 20,
//                           color: Color.fromARGB(240, 0, 131, 143),
//                           fontWeight: FontWeight.bold,
//                         ),
//                       ),

//                       SizedBox(height: 20),

//                       // Section 2: Introduction
//                       Text(
//                         'Please read the terms and conditions carefully before using the Smart Sewa Application. By using this app, you agree to our company terms and conditions.',
//                         textAlign: TextAlign.justify,
//                         style: TextStyle(
//                           fontSize: 18,
//                           height: 1.4, // Adjusted height for consistent spacing
//                           fontWeight: FontWeight.w500,
//                         ),
//                       ),

//                       SizedBox(height: 20),

// // Section 3: Key Terms
//                       Text(
//                         'Key Terms',
//                         style: TextStyle(
//                           fontSize: 20,
//                           fontWeight: FontWeight.bold,
//                           color: Color.fromRGBO(0, 131, 143, 1),
//                         ),
//                       ),
//                       SizedBox(height: 10),
//                       Text(
//                         'The terms apply to everyone using our app.',
//                         style: TextStyle(
//                           fontSize: 18,
//                           height: 1.4, // Consistent line height
//                           fontWeight: FontWeight.w500,
//                         ),
//                         textAlign: TextAlign.justify,
//                       ),

//                       SizedBox(height: 20),

// // Section 4: Your Responsibilities
//                       Text(
//                         'Your Responsibilities',
//                         style: TextStyle(
//                           fontSize: 20,
//                           fontWeight: FontWeight.bold,
//                           color: Color.fromRGBO(0, 131, 143, 1),
//                         ),
//                       ),
//                       SizedBox(height: 10),
//                       Text(
//                         'As a user of Smart Sewa App, you should:\n',
//                         style: TextStyle(
//                           fontSize: 18,
//                           height: 1.4, // Consistent line height
//                           fontWeight: FontWeight.w500,
//                         ),
//                         textAlign: TextAlign.justify,
//                       ),
//                       Text(
//                         '1. Provide correct information when using the application.',
//                         style: TextStyle(
//                           fontSize: 18,
//                           height: 1.4, // Consistent line height
//                           fontWeight: FontWeight.w500,
//                         ),
//                         textAlign: TextAlign.justify,
//                       ),
//                       Text(
//                         '2. Kindly ensure that you use the application in accordance with legal standards and adhere to all guidelines.',
//                         style: TextStyle(
//                           fontSize: 18,
//                           height: 1.4, // Consistent line height
//                           fontWeight: FontWeight.w500,
//                         ),
//                         textAlign: TextAlign.justify,
//                       ),
//                       Text(
//                         '3. Please report any inappropriate behavior you encounter within the application.',
//                         style: TextStyle(
//                           fontSize: 18,
//                           height: 1.4, // Consistent line height
//                           fontWeight: FontWeight.w500,
//                         ),
//                         textAlign: TextAlign.justify,
//                       ),

//                       SizedBox(height: 20),

// // Section 5: Privacy Policy
//                       Text(
//                         'Privacy Policy',
//                         style: TextStyle(
//                           fontSize: 20,
//                           fontWeight: FontWeight.bold,
//                           color: Color.fromRGBO(0, 131, 143, 1),
//                         ),
//                       ),
//                       SizedBox(height: 10),
//                       Text(
//                         // 'We care about your privacy. We collect some information to provide the best service. By using our app, you agree to our Privacy Policy.',
//                         'We care about your privacy. We collect some information to provide the best service. By using our app , you agree.',
//                         style: TextStyle(
//                           fontSize: 18,
//                           height: 1.4, // Consistent line height
//                           fontWeight: FontWeight.w500,
//                         ),
//                         textAlign: TextAlign.justify,
//                       ),

//                       SizedBox(height: 10),

// // Section 6: Disclaimer
//                       Text(
//                         'Disclaimer',
//                         style: TextStyle(
//                           fontSize: 20,
//                           fontWeight: FontWeight.bold,
//                           color: Color.fromRGBO(0, 131, 143, 1),
//                         ),
//                       ),
//                       SizedBox(height: 10),
//                       Text(
//                         'Smart Sewa helps connect service providers with customers. We do not provide the services ourselves. We cannot guarantee the quality of the services offered.',
//                         style: TextStyle(
//                           fontSize: 18,
//                           height: 1.4, // Consistent line height
//                           fontWeight: FontWeight.w500,
//                         ),
//                         textAlign: TextAlign.justify,
//                       ),
//                       Text(
//                         '• We are not responsible for any problems between service providers and customers.',
//                         style: TextStyle(
//                           fontSize: 18,
//                           height: 1.4, // Consistent line height
//                           fontWeight: FontWeight.w500,
//                         ),
//                         textAlign: TextAlign.justify,
//                       ),
//                       Text(
//                         '• We help make sure service providers follow the rules and allow users to rate them.',
//                         style: TextStyle(
//                           fontSize: 18,
//                           height: 1.4, // Consistent line height
//                           fontWeight: FontWeight.w500,
//                         ),
//                         textAlign: TextAlign.justify,
//                       ),

//                       // Additional sections can be added in the same manner
//                     ],
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ));
//   }
// }

import 'package:flutter/material.dart';
import 'package:smartsewa/views/widgets/my_appbar.dart';

class PrivacypolicyScreen extends StatefulWidget {
  const PrivacypolicyScreen({super.key});

  @override
  State<PrivacypolicyScreen> createState() => _PrivacypolicyScreenState();
}

class _PrivacypolicyScreenState extends State<PrivacypolicyScreen> {
  bool _isEnglish = true;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: myAppbar(
          context, true, _isEnglish ? "Terms & Conditions" : "नियम र सर्तहरू"),
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  InkWell(
                    onTap: () {
                      setState(() {
                        _isEnglish = true;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: _isEnglish
                              ? const Color(0xFF00838F)
                              : Colors.grey,
                        ),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        children: [
                          Image.asset(
                            'assets/usa_flag.gif',
                            width: 20,
                            height: 15,
                          ),
                          const SizedBox(width: 4),
                          const Text('EN'),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  InkWell(
                    onTap: () {
                      setState(() {
                        _isEnglish = false;
                      });
                    },
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: !_isEnglish
                              ? const Color(0xFF00838F)
                              : Colors.grey,
                        ),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        children: [
                          Image.asset(
                            'assets/flag_np.png',
                            width: 20,
                            height: 15,
                          ),
                          const SizedBox(width: 4),
                          const Text('NP'),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Expanded(
              child: SingleChildScrollView(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                child: Card(
                  color: Colors.white,
                  margin: const EdgeInsets.symmetric(vertical: 8),
                  elevation: 3,
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _isEnglish
                              ? 'Smart Sewa Solutions Nepal Pvt. Ltd'
                              : 'स्मार्ट सेवा सोलुसन्स नेपाल प्रा. लि.',
                          textAlign: TextAlign.center,
                          style: const TextStyle(
                            fontSize: 20,
                            color: Color.fromARGB(240, 0, 131, 143),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                            _isEnglish
                                ? 'Please read the terms and conditions carefully before using the Smart Sewa Application. By using this app, you agree to our company terms and conditions.'
                                : 'स्मार्ट सेवा एप्लिकेशन प्रयोग गर्नु अधि कृपया नियम र सर्तहरू ध्यानपूर्वक पढ्नुहोस । यो एप प्रयोग गर्दा, तपाई हाम्रो कम्पनीको नियम र सर्तहरू‌मा सहमत हुनहुनेछ।',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                              height: 1.5,
                            )),
                        const SizedBox(height: 16),
                        _buildSectionTitle(
                            _isEnglish ? 'Key Terms' : 'मुख्य नियमहरू'),
                        Text(
                          _isEnglish
                              ? 'The terms apply to everyone using our app.'
                              : 'यी नियमहरू हाम्रो एप प्रयोग गर्ने सबैमा लागु हुनेछ ।',
                          style: _sectionTextStyle(),
                        ),
                        const SizedBox(height: 16),
                        _buildSectionTitle(_isEnglish
                            ? 'Your Responsibilities'
                            : 'तपाईंको जिम्मेवारीहरू'),
                        Text(
                          _isEnglish
                              ? 'As a user of Smart Sewa App, you should:'
                              : 'स्मार्ट सेवा एपको प्रयोगकर्ताको रूपमा, तपाईंले:',
                          style: _sectionTextStyle(),
                        ),
                        _buildBulletPoint(
                          _isEnglish
                              ? '1. Provide correct information when using the application.'
                              : '१. एप्लिकेशन प्रयोग गर्दा सही जानकारी प्रदान गर्नुहोस्।',
                        ),
                        _buildBulletPoint(
                          _isEnglish
                              ? '2. Use the application according to legal standards and guidelines.'
                              : '२. कानुनी मापदण्ड र दिशानिर्देशअनुसार एप्लिकेशन प्रयोग गर्नुहोस्।',
                        ),
                        _buildBulletPoint(
                          _isEnglish
                              ? '3. Report any inappropriate behavior within the application.'
                              : '३. एप्लिकेशनभित्र कुनै अनुचित व्यवहारको रिपोर्ट गर्नुहोस्।',
                        ),
                        const SizedBox(height: 16),
                        _buildSectionTitle(
                            _isEnglish ? 'Privacy Policy' : 'गोपनीयता नीति'),
                        Text(
                          _isEnglish
                              ? 'We care about your privacy. We collect some information to provide the best service. We can send messages for application updates, various types of notices, membership renewals, and promotional notifications By using our app, you agree to our Privacy Policy.'
                              : 'हामी तपाईंको गोपनीयताको ख्याल गर्छौं। उत्कृष्ट सेवा प्रदान गर्न हामी केही जानकारी संकलन गर्छौं। हामी एप अपडेट, सूचना, सदस्यता नवीकरण र प्रचारको जानकारी पठाउन सक्छौं हाम्रो एप प्रयोग गर्दा, तपाईं हाम्रो गोपनीयता नीतिमा सहमत हुनुहुनेछ।',
                          style: _sectionTextStyle(),
                        ),
                        const SizedBox(height: 16),
                        _buildSectionTitle(
                            _isEnglish ? 'Disclaimer' : 'अस्वीकरण'),
                        Text(
                          _isEnglish
                              ? 'Smart Sewa helps connect service providers with customers. We do not provide the services ourselves and cannot guarantee the quality of services offered.'
                              : 'स्मार्ट सेवाले सेवा प्रदायकहरूलाई ग्राहकहरूसँग जोड्न मद्दत गर्दछ। हामी आफैले सेवा प्रदान गर्दैनौं र प्रस्तावित सेवाहरूको गुणस्तरको ग्यारेन्टी दिन सक्दैन।',
                          style: _sectionTextStyle(),
                        ),
                        _buildBulletPoint(
                          _isEnglish
                              ? '• We are not responsible for any issues between service providers and customers.'
                              : '• हामी सेवा प्रदायक र ग्राहकहरू बीचको कुनै पनि समस्याको लागि जिम्मेवार छैनौं।',
                        ),
                        _buildBulletPoint(
                          _isEnglish
                              ? '• We ensure service providers follow the rules and allow users to rate them.'
                              : '• हामी सेवा प्रदायकहरूलाई नियमहरूको पालना गर्न प्रोत्साहित गर्दछौं र प्रयोगकर्ताहरूलाई उनीहरूको मूल्याङ्कन गर्ने अवसर प्रदान गर्छौं।',
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        color: Color.fromRGBO(0, 131, 143, 1),
      ),
    );
  }

  TextStyle _sectionTextStyle() {
    return const TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 16,
      height: 1.5,
    );
  }

  Widget _buildBulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.only(top: 4, left: 16),
      child: Text(
        text,
        style: _sectionTextStyle(),
        textAlign: TextAlign.justify,
      ),
    );
  }
}
