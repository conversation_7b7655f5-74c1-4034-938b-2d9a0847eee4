import 'dart:convert';
import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import 'package:smartsewa/network/base_client.dart';
import 'package:smartsewa/network/models/real_estate_model.dart';
import 'package:smartsewa/views/widgets/custom_toasts.dart';

class RealEstateController extends GetxController {
  final TextEditingController searchController = TextEditingController();
  
  // Observable variables
  final realEstateResponseModel = RealEstateResponseModel().obs;
  final filteredProperties = <Property>[].obs;
  final isLoading = false.obs;
  final selectedCategory = 'All'.obs;
  
  // Base URL
  final baseUrl = BaseClient().baseUrl;
  
  // Property categories
  final List<String> categories = [
    'All',
    'FLAT',
    'HOUSE', 
    'ROOM',
    'LAND',
    'BUSINESS'
  ];

  @override
  void onInit() {
    fetchProperties();
    super.onInit();
  }

  /// Fetch all properties from API
  Future<void> fetchProperties({
    String? category,
    String? searchKeyword,
    String? listingType,
    double? minPrice,
    double? maxPrice,
    String? city,
    int? page = 0,
    int? size = 10,
  }) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? apptoken = prefs.getString("token");
      
      isLoading.value = true;

      // Build query parameters
      Map<String, String> queryParams = {
        'page': page.toString(),
        'size': size.toString(),
      };

      if (category != null && category != 'All') {
        queryParams['category'] = category;
      }
      if (searchKeyword != null && searchKeyword.isNotEmpty) {
        queryParams['searchKeyword'] = searchKeyword;
      }
      if (listingType != null) {
        queryParams['listingType'] = listingType;
      }
      if (minPrice != null) {
        queryParams['minPrice'] = minPrice.toString();
      }
      if (maxPrice != null) {
        queryParams['maxPrice'] = maxPrice.toString();
      }
      if (city != null && city.isNotEmpty) {
        queryParams['city'] = city;
      }

      // Build URI with query parameters
      Uri uri = Uri.parse("$baseUrl/api/v1/properties/")
          .replace(queryParameters: queryParams);

      final response = await http.get(
        uri,
        headers: <String, String>{
          'Content-Type': 'application/json',
          'Authorization': "Bearer $apptoken"
        },
      );

      log("fetchProperties :: ${uri.toString()}");
      log("Status Code: ${response.statusCode}");
      log("Response Body: ${response.body}");

      isLoading.value = false;

      if (response.statusCode == 200) {
        realEstateResponseModel.value = 
            realEstateResponseModelFromJson(response.body);
        filteredProperties.value = 
            realEstateResponseModel.value.properties ?? [];
        successToast(msg: "Properties loaded successfully");
      } else {
        errorToast(msg: "Failed to fetch properties");
      }
    } catch (err) {
      isLoading.value = false;
      log("Error fetching properties: $err");
      errorToast(msg: "Error: ${err.toString()}");
    }
  }

  /// Filter properties by category
  void filterPropertiesByCategory(String category) {
    selectedCategory.value = category;
    if (category == 'All') {
      filteredProperties.value = realEstateResponseModel.value.properties ?? [];
    } else {
      filteredProperties.value = realEstateResponseModel.value.properties
              ?.where((property) => 
                  property.category?.toUpperCase() == category.toUpperCase())
              .toList() ?? [];
    }
  }

  /// Search properties by keyword
  void searchProperties(String keyword) {
    if (keyword.isEmpty) {
      filteredProperties.value = realEstateResponseModel.value.properties ?? [];
    } else {
      filteredProperties.value = realEstateResponseModel.value.properties
              ?.where((property) =>
                  property.title?.toLowerCase().contains(keyword.toLowerCase()) == true ||
                  property.address?.toLowerCase().contains(keyword.toLowerCase()) == true ||
                  property.city?.toLowerCase().contains(keyword.toLowerCase()) == true ||
                  property.description?.toLowerCase().contains(keyword.toLowerCase()) == true)
              .toList() ?? [];
    }
  }

  /// Get property by ID
  Future<Property?> getPropertyById(int propertyId) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String? apptoken = prefs.getString("token");
      
      isLoading.value = true;

      final response = await http.get(
        Uri.parse("$baseUrl/api/v1/properties/$propertyId"),
        headers: <String, String>{
          'Content-Type': 'application/json',
          'Authorization': "Bearer $apptoken"
        },
      );

      log("getPropertyById :: $baseUrl/api/v1/properties/$propertyId");
      log("Status Code: ${response.statusCode}");

      isLoading.value = false;

      if (response.statusCode == 200) {
        final propertyData = json.decode(response.body);
        return Property.fromJson(propertyData);
      } else {
        errorToast(msg: "Failed to fetch property details");
        return null;
      }
    } catch (err) {
      isLoading.value = false;
      log("Error fetching property details: $err");
      errorToast(msg: "Error: ${err.toString()}");
      return null;
    }
  }

  /// Refresh properties
  Future<void> refreshProperties() async {
    await fetchProperties(
      category: selectedCategory.value == 'All' ? null : selectedCategory.value,
      searchKeyword: searchController.text.isEmpty ? null : searchController.text,
    );
  }

  /// Get image URL with base URL
  String getImageUrl(String? imageUrl) {
    if (imageUrl == null || imageUrl.isEmpty) {
      return "https://via.placeholder.com/400x300?text=No+Image";
    }
    
    // If it's already a full URL, return as is
    if (imageUrl.startsWith('http')) {
      return imageUrl;
    }
    
    // Otherwise, prepend the base URL
    return "$baseUrl/api/images/$imageUrl";
  }

  /// Get primary image for a property
  String getPrimaryImageUrl(Property property) {
    if (property.images != null && property.images!.isNotEmpty) {
      // Find primary image
      final primaryImage = property.images!.firstWhere(
        (img) => img.isPrimary == true,
        orElse: () => property.images!.first,
      );
      return getImageUrl(primaryImage.imageUrl);
    }
    return "https://via.placeholder.com/400x300?text=No+Image";
  }

  /// Format price
  String formatPrice(double? price, String? listingType) {
    if (price == null) return "Price not available";
    
    String formattedPrice = "NPR ${price.toStringAsFixed(0)}";
    
    if (listingType?.toUpperCase() == "RENT") {
      formattedPrice += "/month";
    }
    
    return formattedPrice;
  }

  /// Get facility icons
  IconData getFacilityIcon(String facilityType) {
    switch (facilityType.toUpperCase()) {
      case 'PARKING':
        return Icons.local_parking;
      case 'WIFI':
        return Icons.wifi;
      case 'ELEVATOR':
        return Icons.elevator;
      case 'GARDEN':
        return Icons.local_florist;
      case 'SECURITY':
        return Icons.security;
      case 'GYM':
        return Icons.fitness_center;
      case 'POOL':
        return Icons.pool;
      default:
        return Icons.check_circle;
    }
  }

  /// Clear search
  void clearSearch() {
    searchController.clear();
    filteredProperties.value = realEstateResponseModel.value.properties ?? [];
  }

  @override
  void onClose() {
    searchController.dispose();
    super.onClose();
  }
}
