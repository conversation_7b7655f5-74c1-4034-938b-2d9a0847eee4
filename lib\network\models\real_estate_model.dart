// To parse this JSON data, do
//
//     final realEstateResponseModel = realEstateResponseModelFromJson(jsonString);

import 'dart:convert';

RealEstateResponseModel realEstateResponseModelFromJson(String str) =>
    RealEstateResponseModel.fromJson(json.decode(str));

String realEstateResponseModelToJson(RealEstateResponseModel data) =>
    json.encode(data.toJson());

class RealEstateResponseModel {
  List<Property>? properties;
  int? currentPage;
  int? totalPages;
  int? totalElements;
  int? pageSize;
  bool? hasNext;
  bool? hasPrevious;
  String? searchKeyword;
  int? resultsCount;
  double? searchLatitude;
  double? searchLongitude;
  String? appliedFilters;

  RealEstateResponseModel({
    this.properties,
    this.currentPage,
    this.totalPages,
    this.totalElements,
    this.pageSize,
    this.hasNext,
    this.hasPrevious,
    this.searchKeyword,
    this.resultsCount,
    this.searchLatitude,
    this.searchLongitude,
    this.appliedFilters,
  });

  factory RealEstateResponseModel.fromJson(Map<String, dynamic> json) =>
      RealEstateResponseModel(
        properties: json["properties"] == null
            ? []
            : List<Property>.from(
                json["properties"]!.map((x) => Property.fromJson(x))),
        currentPage: json["currentPage"],
        totalPages: json["totalPages"],
        totalElements: json["totalElements"],
        pageSize: json["pageSize"],
        hasNext: json["hasNext"],
        hasPrevious: json["hasPrevious"],
        searchKeyword: json["searchKeyword"],
        resultsCount: json["resultsCount"],
        searchLatitude: json["searchLatitude"]?.toDouble(),
        searchLongitude: json["searchLongitude"]?.toDouble(),
        appliedFilters: json["appliedFilters"],
      );

  Map<String, dynamic> toJson() => {
        "properties": properties == null
            ? []
            : List<dynamic>.from(properties!.map((x) => x.toJson())),
        "currentPage": currentPage,
        "totalPages": totalPages,
        "totalElements": totalElements,
        "pageSize": pageSize,
        "hasNext": hasNext,
        "hasPrevious": hasPrevious,
        "searchKeyword": searchKeyword,
        "resultsCount": resultsCount,
        "searchLatitude": searchLatitude,
        "searchLongitude": searchLongitude,
        "appliedFilters": appliedFilters,
      };
}

class Property {
  int? propertyId;
  String? title;
  String? category;
  String? listingType;
  String? description;
  double? price;
  bool? isNegotiable;
  double? latitude;
  double? longitude;
  String? address;
  String? city;
  String? district;
  int? floorNumber;
  double? landArea;
  String? roadSize;
  String? roadType;
  int? bathroomCount;
  String? ownerContact;
  bool? isActive;
  bool? isFeatured;
  bool? isBanner;
  DateTime? createdDate;
  DateTime? expiryDate;
  int? viewCount;
  int? userId;
  List<PropertyImage>? images;
  List<Facility>? facilities;
  int? chatCount;
  double? distanceFromUser;
  double? recommendationScore;

  Property({
    this.propertyId,
    this.title,
    this.category,
    this.listingType,
    this.description,
    this.price,
    this.isNegotiable,
    this.latitude,
    this.longitude,
    this.address,
    this.city,
    this.district,
    this.floorNumber,
    this.landArea,
    this.roadSize,
    this.roadType,
    this.bathroomCount,
    this.ownerContact,
    this.isActive,
    this.isFeatured,
    this.isBanner,
    this.createdDate,
    this.expiryDate,
    this.viewCount,
    this.userId,
    this.images,
    this.facilities,
    this.chatCount,
    this.distanceFromUser,
    this.recommendationScore,
  });

  factory Property.fromJson(Map<String, dynamic> json) => Property(
        propertyId: json["propertyId"],
        title: json["title"],
        category: json["category"],
        listingType: json["listingType"],
        description: json["description"],
        price: json["price"]?.toDouble(),
        isNegotiable: json["isNegotiable"],
        latitude: json["latitude"]?.toDouble(),
        longitude: json["longitude"]?.toDouble(),
        address: json["address"],
        city: json["city"],
        district: json["district"],
        floorNumber: json["floorNumber"],
        landArea: json["landArea"]?.toDouble(),
        roadSize: json["roadSize"],
        roadType: json["roadType"],
        bathroomCount: json["bathroomCount"],
        ownerContact: json["ownerContact"],
        isActive: json["isActive"],
        isFeatured: json["isFeatured"],
        isBanner: json["isBanner"],
        createdDate: json["createdDate"] == null
            ? null
            : DateTime.parse(json["createdDate"]),
        expiryDate: json["expiryDate"] == null
            ? null
            : DateTime.parse(json["expiryDate"]),
        viewCount: json["viewCount"],
        userId: json["userId"],
        images: json["images"] == null
            ? []
            : List<PropertyImage>.from(
                json["images"]!.map((x) => PropertyImage.fromJson(x))),
        facilities: json["facilities"] == null
            ? []
            : List<Facility>.from(
                json["facilities"]!.map((x) => Facility.fromJson(x))),
        chatCount: json["chatCount"],
        distanceFromUser: json["distanceFromUser"]?.toDouble(),
        recommendationScore: json["recommendationScore"]?.toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "propertyId": propertyId,
        "title": title,
        "category": category,
        "listingType": listingType,
        "description": description,
        "price": price,
        "isNegotiable": isNegotiable,
        "latitude": latitude,
        "longitude": longitude,
        "address": address,
        "city": city,
        "district": district,
        "floorNumber": floorNumber,
        "landArea": landArea,
        "roadSize": roadSize,
        "roadType": roadType,
        "bathroomCount": bathroomCount,
        "ownerContact": ownerContact,
        "isActive": isActive,
        "isFeatured": isFeatured,
        "isBanner": isBanner,
        "createdDate": createdDate?.toIso8601String(),
        "expiryDate": expiryDate?.toIso8601String(),
        "viewCount": viewCount,
        "userId": userId,
        "images": images == null
            ? []
            : List<dynamic>.from(images!.map((x) => x.toJson())),
        "facilities": facilities == null
            ? []
            : List<dynamic>.from(facilities!.map((x) => x.toJson())),
        "chatCount": chatCount,
        "distanceFromUser": distanceFromUser,
        "recommendationScore": recommendationScore,
      };
}

class Facility {
  int? facilityId;
  String? facilityType;
  String? facilityValue;
  int? propertyId;

  Facility({
    this.facilityId,
    this.facilityType,
    this.facilityValue,
    this.propertyId,
  });

  factory Facility.fromJson(Map<String, dynamic> json) => Facility(
        facilityId: json["facilityId"],
        facilityType: json["facilityType"],
        facilityValue: json["facilityValue"],
        propertyId: json["propertyId"],
      );

  Map<String, dynamic> toJson() => {
        "facilityId": facilityId,
        "facilityType": facilityType,
        "facilityValue": facilityValue,
        "propertyId": propertyId,
      };
}

class PropertyImage {
  int? imageId;
  String? imageUrl;
  String? imageName;
  bool? isPrimary;
  DateTime? uploadDate;
  double? qualityScore;
  int? propertyId;

  PropertyImage({
    this.imageId,
    this.imageUrl,
    this.imageName,
    this.isPrimary,
    this.uploadDate,
    this.qualityScore,
    this.propertyId,
  });

  factory PropertyImage.fromJson(Map<String, dynamic> json) => PropertyImage(
        imageId: json["imageId"],
        imageUrl: json["imageUrl"],
        imageName: json["imageName"],
        isPrimary: json["isPrimary"],
        uploadDate: json["uploadDate"] == null
            ? null
            : DateTime.parse(json["uploadDate"]),
        qualityScore: json["qualityScore"]?.toDouble(),
        propertyId: json["propertyId"],
      );

  Map<String, dynamic> toJson() => {
        "imageId": imageId,
        "imageUrl": imageUrl,
        "imageName": imageName,
        "isPrimary": isPrimary,
        "uploadDate": uploadDate?.toIso8601String(),
        "qualityScore": qualityScore,
        "propertyId": propertyId,
      };
}
