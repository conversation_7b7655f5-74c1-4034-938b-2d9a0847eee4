// import 'dart:async';
// import 'dart:convert';
// import 'dart:io';

// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:get/get.dart';
// import 'package:http/http.dart' as http;
// import 'package:smartsewa/core/development/console.dart';
// import 'package:smartsewa/network/base_client.dart';
// import 'package:smartsewa/views/widgets/custom_toasts.dart';

// import '../../../network/services/authServices/auth_controller.dart';
// import '../../../views/widgets/buttons/app_buttons.dart';
// import 'forget_otp_screen.dart';

// class ForgetPassword extends StatefulWidget {
//   const ForgetPassword({super.key});

//   @override
//   State<ForgetPassword> createState() => _ForgetPasswordState();
// }

// class _ForgetPasswordState extends State<ForgetPassword> {
//   final _formKey = GlobalKey<FormState>();

//   int randomNumber = 0;
//   final TextEditingController phoneController = TextEditingController();
//   final authController = Get.put(AuthController());
//   String baseUrl = BaseClient().baseUrl;
//   var isLoading = false.obs;

//   ///////////////////////////////  validation for password //////////////////////////////////////
//   bool _containsUppercase(String value) {
//     return value.contains(RegExp(r'[A-Z]'));
//   }

//   bool _containsLowercase(String value) {
//     return value.contains(RegExp(r'[a-z]'));
//   }

//   bool _containsNumber(String value) {
//     return value.contains(RegExp(r'[0-9]'));
//   }

//   bool _containsSpecialCharacter(String value) {
//     return value.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
//   }

//   @override
//   Widget build(BuildContext context) {
//     Size size = MediaQuery.of(context).size;
//     return Scaffold(
//       body: Stack(
//         children: [
//           // Background Gradient
//           Container(
//             decoration: const BoxDecoration(
//               gradient: LinearGradient(
//                 colors: [
//                   Color.fromARGB(255, 255, 255, 255),
//                   Color.fromARGB(255, 255, 255, 255)
//                 ],
//                 begin: Alignment.topCenter,
//                 end: Alignment.bottomCenter,
//               ),
//             ),
//           ),
//           Center(
//             child: Container(
//               width: size.width * 0.9,
//               padding: EdgeInsets.all(size.width * 0.05),
//               decoration: BoxDecoration(
//                 color: Colors.white,
//                 borderRadius: BorderRadius.circular(20),
//                 boxShadow: [
//                   BoxShadow(
//                     color: Colors.black.withOpacity(0.2),
//                     blurRadius: 15,
//                     offset: const Offset(0, 5),
//                   ),
//                 ],
//               ),
//               child: Form(
//                 key: _formKey,
//                 child: Padding(
//                   padding: EdgeInsets.all(size.aspectRatio * 20),
//                   child: SingleChildScrollView(
//                     child: Column(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         SizedBox(height: size.height * 0.05),
//                         Image.asset(
//                           'assets/Logo.png',
//                           height: size.height * 0.15,
//                         ),
//                         SizedBox(height: size.height * 0.03),
//                         const Text(
//                           "Enter your phone number to get verification code",
//                           textAlign: TextAlign.center,
//                           style: TextStyle(
//                             fontSize: 22,
//                             fontWeight: FontWeight.w700,
//                             color: Color(0xFF00796B),
//                           ),
//                         ),
//                         SizedBox(height: size.height * 0.03),
//                         // Input field with icon and rounded styling
//                         Container(
//                           decoration: BoxDecoration(
//                             color: Colors.grey[100],
//                             borderRadius: BorderRadius.circular(18),
//                             boxShadow: [
//                               BoxShadow(
//                                 color: Colors.grey.withOpacity(0.1),
//                                 spreadRadius: 1,
//                                 blurRadius: 5,
//                                 offset: const Offset(0, 2),
//                               ),
//                             ],
//                           ),
//                           child: Padding(
//                             padding:
//                                 const EdgeInsets.symmetric(horizontal: 16.0),
//                             child: Row(
//                               children: [
//                                 const Icon(Icons.phone,
//                                     color: Color(0xFF009688)),
//                                 Expanded(
//                                   child: TextFormField(
//                                     controller: phoneController,
//                                     validator: (password) {
//                                       if (password!.isEmpty ||
//                                           password.length != 10) {
//                                         return 'Enter a valid number';
//                                       }
//                                       if (!RegExp(r'^(98|97|96)\d{8}$')
//                                           .hasMatch(password)) {
//                                         return 'Phone number must start with 98, 97, or 96';
//                                       }
//                                       return null;
//                                     },
//                                     textAlign: TextAlign.center,
//                                     inputFormatters: [
//                                       FilteringTextInputFormatter.allow(
//                                           RegExp(r'\d')),
//                                       FilteringTextInputFormatter.digitsOnly,
//                                     ],
//                                     keyboardType: TextInputType.phone,
//                                     style: const TextStyle(
//                                         color: Colors.black87, fontSize: 16),
//                                     decoration: InputDecoration(
//                                       filled: true,
//                                       fillColor: Colors.transparent,
//                                       border: InputBorder.none,
//                                       hintText: 'Enter Your Mobile Number',
//                                       hintStyle: TextStyle(
//                                         color: Colors.grey[500],
//                                         fontSize: 16,
//                                       ),
//                                     ),
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ),
//                         ),
//                         SizedBox(height: size.height * 0.05),
//                         // Elevated Button with Animation
//                         GestureDetector(
//                           onTap: () async {
//                             try {
//                               var request = http.MultipartRequest(
//                                 'POST',
//                                 Uri.parse(
//                                     '$baseUrl/api/v1/auth/${phoneController.text}/forget-password'),
//                               );
//                               http.StreamedResponse response =
//                                   await request.send().timeout(
//                                         const Duration(seconds: 20),
//                                         onTimeout: () => throw TimeoutException(
//                                             'Request timeout'),
//                                       );
//                               String responseBody =
//                                   await response.stream.bytesToString();

//                               consolelog(response.statusCode);
//                               if (response.statusCode == 200) {
//                                 successToast(
//                                     msg:
//                                         '${jsonDecode(responseBody)['message']}');
//                                 Get.to(() => ForgetOtpScreen(
//                                     number: 1, phone: phoneController.text));
//                               } else {
//                                 errorToast(
//                                     msg:
//                                         '${jsonDecode(responseBody)['message']}');
//                                 consolelog(response.reasonPhrase);
//                               }
//                             } catch (e) {
//                               if (e is TimeoutException) {
//                                 errorToast(
//                                     msg: 'Request Timeout: ${e.message}');
//                                 isLoading.value = false;
//                               } else if (e is SocketException) {
//                                 errorToast(msg: 'Network Error: ${e.message}');
//                                 isLoading.value = false;
//                               } else if (e is http.ClientException) {
//                                 errorToast(msg: 'Client Error: ${e.message}');
//                                 isLoading.value = false;
//                               } else {
//                                 errorToast(
//                                     msg: 'Unexpected error: ${e.toString()}');
//                                 isLoading.value = false;
//                               }
//                             }
//                           },
//                           child: AnimatedContainer(
//                             duration: const Duration(milliseconds: 300),
//                             width: double.infinity,
//                             padding: EdgeInsets.symmetric(
//                                 vertical: size.height * 0.015),
//                             decoration: BoxDecoration(
//                               gradient: const LinearGradient(
//                                 colors: [Color(0xFF00BCD4), Color(0xFF009688)],
//                                 begin: Alignment.centerLeft,
//                                 end: Alignment.centerRight,
//                               ),
//                               borderRadius: BorderRadius.circular(25),
//                             ),
//                             child: const Center(
//                               child: Text(
//                                 'Continue',
//                                 style: TextStyle(
//                                   color: Colors.white,
//                                   fontSize: 18,
//                                   fontWeight: FontWeight.w600,
//                                 ),
//                               ),
//                             ),
//                           ),
//                         ),
//                         SizedBox(height: size.height * 0.05),
//                       ],
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

// import 'dart:async';
// import 'dart:convert';
// import 'dart:io';

// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:get/get.dart';
// import 'package:http/http.dart' as http;
// import 'package:smartsewa/core/development/console.dart';
// import 'package:smartsewa/network/base_client.dart';
// import 'package:smartsewa/views/widgets/custom_toasts.dart';
// import 'package:smartsewa/views/widgets/my_appbar.dart';

// import '../../../network/services/authServices/auth_controller.dart';
// import 'forget_otp_screen.dart';

// class ForgetPassword extends StatefulWidget {
//   const ForgetPassword({super.key});

//   @override
//   State<ForgetPassword> createState() => _ForgetPasswordState();
// }

// class _ForgetPasswordState extends State<ForgetPassword> {
//   final _formKey = GlobalKey<FormState>();

//   int randomNumber = 0;
//   final TextEditingController phoneController = TextEditingController();
//   final authController = Get.put(AuthController());
//   String baseUrl = BaseClient().baseUrl;
//   var isLoading = false.obs;

//   ///////////////////////////////  validation for password //////////////////////////////////////
//   bool _containsUppercase(String value) {
//     return value.contains(RegExp(r'[A-Z]'));
//   }

//   bool _containsLowercase(String value) {
//     return value.contains(RegExp(r'[a-z]'));
//   }

//   bool _containsNumber(String value) {
//     return value.contains(RegExp(r'[0-9]'));
//   }

//   bool _containsSpecialCharacter(String value) {
//     return value.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'));
//   }

//   @override
//   Widget build(BuildContext context) {
//     Size size = MediaQuery.of(context).size;
//     return Scaffold(
//       appBar: myAppbar(context, true, ""),
//       body: Stack(
//         children: [
//           // Background Gradient
//           Container(
//             decoration: const BoxDecoration(
//               gradient: LinearGradient(
//                 colors: [
//                   Color.fromARGB(255, 255, 255, 255),
//                   Color.fromARGB(255, 255, 255, 255)
//                 ],
//                 begin: Alignment.topCenter,
//                 end: Alignment.bottomCenter,
//               ),
//             ),
//           ),
//           Center(
//             child: Container(
//               width: size.width * 0.9,
//               padding: EdgeInsets.all(size.width * 0.05),
//               decoration: BoxDecoration(
//                 color: Colors.white,
//                 borderRadius: BorderRadius.circular(20),
//                 boxShadow: [
//                   BoxShadow(
//                     color: Colors.black.withOpacity(0.2),
//                     blurRadius: 15,
//                     offset: const Offset(0, 5),
//                   ),
//                 ],
//               ),
//               child: Form(
//                 key: _formKey,
//                 child: Padding(
//                   padding: EdgeInsets.all(size.aspectRatio * 30),
//                   child: SingleChildScrollView(
//                     child: Column(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         // Back Button inside the container

//                         SizedBox(height: size.height * 0.05),
//                         Image.asset(
//                           'assets/Logo.png',
//                           height: size.height * 0.15,
//                         ),
//                         SizedBox(height: size.height * 0.03),
//                         const Text(
//                           "Enter your phone number to get verification code",
//                           textAlign: TextAlign.center,
//                           style: TextStyle(
//                             fontSize: 22,
//                             fontWeight: FontWeight.w700,
//                             color: Color(0xFF00796B),
//                           ),
//                         ),
//                         SizedBox(height: size.height * 0.03),
//                         // Input field with icon and rounded styling
//                         Container(
//                           decoration: BoxDecoration(
//                             color: Colors.grey[100],
//                             borderRadius: BorderRadius.circular(18),
//                             boxShadow: [
//                               BoxShadow(
//                                 color: Colors.grey.withOpacity(0.1),
//                                 spreadRadius: 1,
//                                 blurRadius: 5,
//                                 offset: const Offset(0, 2),
//                               ),
//                             ],
//                           ),
//                           child: Padding(
//                             padding:
//                                 const EdgeInsets.symmetric(horizontal: 16.0),
//                             child: Row(
//                               children: [
//                                 const Icon(Icons.phone,
//                                     color: Color(0xFF009688)),
//                                 Expanded(
//                                   child: TextFormField(
//                                     controller: phoneController,
//                                     validator: (password) {
//                                       if (password!.isEmpty ||
//                                           password.length != 10) {
//                                         return 'Enter a valid number';
//                                       }
//                                       if (!RegExp(r'^(98|97|96)\d{8}$')
//                                           .hasMatch(password)) {
//                                         return 'Phone number must start with 98, 97, or 96';
//                                       }
//                                       return null;
//                                     },
//                                     textAlign: TextAlign.center,
//                                     inputFormatters: [
//                                       FilteringTextInputFormatter.allow(
//                                           RegExp(r'\d')),
//                                       FilteringTextInputFormatter.digitsOnly,
//                                     ],
//                                     keyboardType: TextInputType.phone,
//                                     style: const TextStyle(
//                                         color: Colors.black87, fontSize: 16),
//                                     decoration: InputDecoration(
//                                       filled: true,
//                                       fillColor: Colors.transparent,
//                                       border: InputBorder.none,
//                                       hintText: 'Enter Your Mobile Number',
//                                       hintStyle: TextStyle(
//                                         color: Colors.grey[500],
//                                         fontSize: 16,
//                                       ),
//                                     ),
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ),
//                         ),
//                         SizedBox(height: size.height * 0.05),
//                         // Elevated Button with Animation
//                         GestureDetector(

//                           onTap: () async {

//                              if (phoneController.text.length ==10) {

//                             try {

//                               var request = http.MultipartRequest(
//                                 'POST',
//                                 Uri.parse(
//                                     '$baseUrl/api/v1/auth/${phoneController.text}/forget-password'),
//                               );
//                               http.StreamedResponse response =
//                                   await request.send().timeout(
//                                         const Duration(seconds: 20),
//                                         onTimeout: () => throw TimeoutException(
//                                             'Request timeout'),
//                                       );
//                               String responseBody =
//                                   await response.stream.bytesToString();

//                               consolelog(response.statusCode);
//                               if (response.statusCode == 200) {
//                                 successToast(
//                                     msg:
//                                         '${jsonDecode(responseBody)['message']}');
//                                 Get.to(() => ForgetOtpScreen(
//                                     number: 1, phone: phoneController.text));
//                               } else {
//                                 errorToast(
//                                     msg:
//                                         '${jsonDecode(responseBody)['message']}');
//                                 consolelog(response.reasonPhrase);
//                               }
//                             } catch (e) {
//                               if (e is TimeoutException) {
//                                 errorToast(
//                                     msg: 'Request Timeout: ${e.message}');
//                                 isLoading.value = false;
//                               } else if (e is SocketException) {
//                                 errorToast(msg: 'Network Error: ${e.message}');
//                                 isLoading.value = false;
//                               } else if (e is http.ClientException) {
//                                 errorToast(msg: 'Client Error: ${e.message}');
//                                 isLoading.value = false;
//                               } else {

//                                 errorToast(
//                                     msg: 'Unexpected error');
//                                 isLoading.value = false;
//                               }
//                             }
//                           } else { errorToast(msg: "Enter valid mobile number");}
//   }
//                           ,
//                           child: AnimatedContainer(
//                             duration: const Duration(milliseconds: 300),
//                             width: double.infinity,
//                             padding: EdgeInsets.symmetric(
//                                 vertical: size.height * 0.015),
//                             decoration: BoxDecoration(
//                               gradient: const LinearGradient(
//                                 colors: [Color(0xFF00BCD4), Color(0xFF009688)],
//                                 begin: Alignment.centerLeft,
//                                 end: Alignment.centerRight,
//                               ),
//                               borderRadius: BorderRadius.circular(25),
//                             ),
//                             child: const Center(
//                               child: Text(
//                                 'Continue',
//                                 style: TextStyle(
//                                   color: Colors.white,
//                                   fontSize: 18,
//                                   fontWeight: FontWeight.w600,
//                                 ),
//                               ),
//                             ),
//                           ),
//                         ),
//                         SizedBox(height: size.height * 0.05),
//                       ],
//                     ),
//                   ),
//                 ),
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:smartsewa/core/development/console.dart';
import 'package:smartsewa/network/base_client.dart';
import 'package:smartsewa/views/widgets/custom_toasts.dart';
import 'package:smartsewa/views/widgets/my_appbar.dart';

import '../../../network/services/authServices/auth_controller.dart';
import 'forget_otp_screen.dart';

class ForgetPassword extends StatefulWidget {
  const ForgetPassword({super.key});

  @override
  State<ForgetPassword> createState() => _ForgetPasswordState();
}

class _ForgetPasswordState extends State<ForgetPassword> {
  final _formKey = GlobalKey<FormState>();

  int randomNumber = 0;
  final TextEditingController phoneController = TextEditingController();
  final authController = Get.put(AuthController());
  String baseUrl = BaseClient().baseUrl;
  var isLoading = false.obs;

  @override
  Widget build(BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return Scaffold(
      appBar: myAppbar(context, true, ""),
      body: Stack(
        children: [
          // Background Gradient
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color.fromARGB(255, 255, 255, 255),
                  Color.fromARGB(255, 255, 255, 255)
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
          Center(
            child: Container(
              width: size.width * 0.9,
              padding: EdgeInsets.all(size.width * 0.05),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.2),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Form(
                key: _formKey,
                child: Padding(
                  padding: EdgeInsets.all(size.aspectRatio * 30),
                  child: SingleChildScrollView(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Back Button inside the container

                        SizedBox(height: size.height * 0.05),
                        Image.asset(
                          'assets/Logo.png',
                          height: size.height * 0.15,
                        ),
                        SizedBox(height: size.height * 0.03),
                        const Text(
                          "Enter your phone number to get verification code",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 22,
                            fontWeight: FontWeight.w700,
                            color: Color(0xFF00796B),
                          ),
                        ),
                        SizedBox(height: size.height * 0.03),
                        // Input field with icon and rounded styling
                        Container(
                          decoration: BoxDecoration(
                            color: Colors.grey[100],
                            borderRadius: BorderRadius.circular(18),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.grey.withOpacity(0.1),
                                spreadRadius: 1,
                                blurRadius: 5,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 16.0),
                            child: Row(
                              children: [
                                const Icon(Icons.phone,
                                    color: Color(0xFF009688)),
                                Expanded(
                                  child: TextFormField(
                                    controller: phoneController,
                                    validator: (phone) {
                                      if (phone!.isEmpty ||
                                          phone.length != 10) {
                                        return 'Enter a valid number';
                                      }
                                      if (!RegExp(r'^(98|97|96)\d{8}$')
                                          .hasMatch(phone)) {
                                        return 'Phone number must start with 98, 97, or 96';
                                      }
                                      return null;
                                    },
                                    textAlign: TextAlign.center,
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                          RegExp(r'\d')),
                                      FilteringTextInputFormatter.digitsOnly,
                                    ],
                                    keyboardType: TextInputType.phone,
                                    style: const TextStyle(
                                        color: Colors.black87, fontSize: 16),
                                    decoration: InputDecoration(
                                      filled: true,
                                      fillColor: Colors.transparent,
                                      border: InputBorder.none,
                                      hintText: 'Enter Your Mobile Number',
                                      hintStyle: TextStyle(
                                        color: Colors.grey[500],
                                        fontSize: 16,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: size.height * 0.05),
                        // Elevated Button with Animation
                        GestureDetector(
                          onTap: () async {
                            if (phoneController.text.length == 10) {
                              try {
                                var request = http.MultipartRequest(
                                  'POST',
                                  Uri.parse(
                                      '$baseUrl/api/v1/auth/${phoneController.text}/forget-password'),
                                );
                                http.StreamedResponse response =
                                    await request.send().timeout(
                                          const Duration(seconds: 20),
                                          onTimeout: () =>
                                              throw TimeoutException(
                                                  'Request timeout'),
                                        );
                                String responseBody =
                                    await response.stream.bytesToString();

                                consolelog(response.statusCode);
                                if (response.statusCode == 200) {
                                  successToast(
                                      msg:
                                          '${jsonDecode(responseBody)['message']}');
                                  Get.to(() => ForgetOtpScreen(
                                      number: 1, phone: phoneController.text));
                                } else {
                                  errorToast(
                                      msg:
                                          '${jsonDecode(responseBody)['message']}');
                                  consolelog(response.reasonPhrase);
                                }
                              } catch (e) {
                                if (e is TimeoutException) {
                                  errorToast(
                                      msg: 'Request Timeout: ${e.message}');
                                  isLoading.value = false;
                                } else if (e is SocketException) {
                                  errorToast(
                                      msg: 'Network Error: ${e.message}');
                                  isLoading.value = false;
                                } else if (e is http.ClientException) {
                                  errorToast(msg: 'Client Error: ${e.message}');
                                  isLoading.value = false;
                                } else {
                                  errorToast(msg: 'Unexpected error');
                                  isLoading.value = false;
                                }
                              }
                            } else {
                              errorToast(msg: "Enter valid mobile number");
                            }
                          },
                          child: AnimatedContainer(
                            duration: const Duration(milliseconds: 300),
                            width: double.infinity,
                            padding: EdgeInsets.symmetric(
                                vertical: size.height * 0.015),
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [Color(0xFF00BCD4), Color(0xFF009688)],
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                              ),
                              borderRadius: BorderRadius.circular(25),
                            ),
                            child: const Center(
                              child: Text(
                                'Continue',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ),
                        SizedBox(height: size.height * 0.05),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
