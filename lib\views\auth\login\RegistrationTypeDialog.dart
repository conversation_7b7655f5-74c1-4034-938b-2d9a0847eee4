import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:smartsewa/views/auth/registration/user_registration.dart';

class RegistrationTypeDialog {
  static void show(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final width = size.width;
    final height = size.height;

    // Enhanced responsive breakpoints
    final isMobile = width < 600;
    final isTablet = width >= 600 && width < 1024;
    final isDesktop = width >= 1024;

    // Define the variables locally for this dialog
    bool isServiceProvider = false;
    bool hasSelectedType = false;

    showDialog(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.black.withOpacity(0.6),
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return Dialog(
              backgroundColor: Colors.transparent,
              elevation: 0,
              child: Container(
                width: isDesktop
                    ? width * 0.45
                    : (isTablet ? width * 0.65 : width * 0.9),
                constraints: BoxConstraints(
                  maxWidth: isDesktop ? 550 : (isTablet ? 500 : width * 0.95),
                  maxHeight: height * 0.85,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(
                      isDesktop ? 28 : (isTablet ? 24 : 20)),
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.white,
                      Colors.grey.shade50,
                      Colors.white,
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.15),
                      blurRadius: isDesktop ? 30 : 20,
                      offset: const Offset(0, 10),
                      spreadRadius: 0,
                    ),
                    BoxShadow(
                      color: Colors.black.withOpacity(0.08),
                      blurRadius: isDesktop ? 60 : 40,
                      offset: const Offset(0, 20),
                      spreadRadius: 0,
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(
                      isDesktop ? 28 : (isTablet ? 24 : 20)),
                  child: SingleChildScrollView(
                    physics: const BouncingScrollPhysics(),
                    child: Padding(
                      padding: EdgeInsets.all(
                        isDesktop ? 32 : (isTablet ? 28 : 24),
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Container(
                                          width: isDesktop
                                              ? 6
                                              : (isTablet ? 5 : 4),
                                          height: isDesktop
                                              ? 32
                                              : (isTablet ? 28 : 24),
                                          decoration: BoxDecoration(
                                            borderRadius:
                                                BorderRadius.circular(8),
                                            gradient: const LinearGradient(
                                              colors: [
                                                Color(0xFF00838F),
                                                Color(0xFF0097A7),
                                              ],
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                            width: isDesktop
                                                ? 16
                                                : (isTablet ? 14 : 12)),
                                        Expanded(
                                          child: Text(
                                            "Create Account",
                                            style: TextStyle(
                                              fontSize: isDesktop
                                                  ? 28
                                                  : (isTablet ? 24 : 20),
                                              fontWeight: FontWeight.w700,
                                              color: const Color(0xFF1A202C),
                                              letterSpacing: -0.5,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                  width: isDesktop ? 16 : (isTablet ? 14 : 12)),
                              Container(
                                width: isDesktop ? 44 : (isTablet ? 40 : 36),
                                height: isDesktop ? 44 : (isTablet ? 40 : 36),
                                decoration: BoxDecoration(
                                  color: Colors.grey.shade100,
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: Colors.grey.shade200,
                                    width: 1,
                                  ),
                                ),
                                child: Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(12),
                                    onTap: () => Navigator.pop(context),
                                    child: Icon(
                                      Icons.close_rounded,
                                      color: const Color(0xFF64748B),
                                      size:
                                          isDesktop ? 20 : (isTablet ? 18 : 16),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),

                          SizedBox(
                              height: isDesktop ? 40 : (isTablet ? 35 : 30)),

                          // Selection hint text - only show when no selection is made
                          if (!hasSelectedType)
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal:
                                    isDesktop ? 16 : (isTablet ? 14 : 12),
                                vertical: isDesktop ? 12 : (isTablet ? 10 : 8),
                              ),
                              decoration: BoxDecoration(
                                color:
                                    const Color(0xFF00838F).withOpacity(0.08),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color:
                                      const Color(0xFF00838F).withOpacity(0.2),
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.info_outline_rounded,
                                    color: const Color(0xFF00838F),
                                    size: isDesktop ? 20 : (isTablet ? 18 : 16),
                                  ),
                                  SizedBox(
                                      width:
                                          isDesktop ? 12 : (isTablet ? 10 : 8)),
                                  Expanded(
                                    child: Text(
                                      "Please select an account type to continue",
                                      style: TextStyle(
                                        fontSize: isDesktop
                                            ? 14
                                            : (isTablet ? 13 : 12),
                                        color: const Color(0xFF00838F),
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),

                          if (!hasSelectedType)
                            SizedBox(
                                height: isDesktop ? 20 : (isTablet ? 18 : 16)),

                          // Enhanced User Type Selection with cards
                          Container(
                            padding: EdgeInsets.all(
                                isDesktop ? 8 : (isTablet ? 6 : 4)),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: hasSelectedType
                                    ? const Color(0xFF00838F).withOpacity(0.3)
                                    : Colors.grey.shade200,
                                width: hasSelectedType ? 2 : 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                // User Option
                                Expanded(
                                  child: _buildSelectionCard(
                                    isSelected:
                                        !isServiceProvider && hasSelectedType,
                                    title: "Normal User",
                                    subtitle: "Browse & book services",
                                    icon: Icons.person_outline_rounded,
                                    onTap: () {
                                      setState(() {
                                        isServiceProvider = false;
                                        hasSelectedType = true;
                                      });
                                    },
                                    context: context,
                                    showHint: !hasSelectedType,
                                  ),
                                ),
                                SizedBox(
                                    width: isDesktop ? 8 : (isTablet ? 6 : 4)),
                                Expanded(
                                  child: _buildSelectionCard(
                                    isSelected:
                                        isServiceProvider && hasSelectedType,
                                    title: "Service Provider",
                                    subtitle: "Offer your services",
                                    icon: Icons.business_center_outlined,
                                    onTap: () {
                                      setState(() {
                                        isServiceProvider = true;
                                        hasSelectedType = true;
                                      });
                                    },
                                    context: context,
                                    showHint: !hasSelectedType,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                              height: isDesktop ? 40 : (isTablet ? 35 : 30)),
                          AnimatedContainer(
                            duration: const Duration(milliseconds: 300),
                            height: isDesktop ? 56 : (isTablet ? 52 : 48),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(16),
                              gradient: hasSelectedType
                                  ? const LinearGradient(
                                      colors: [
                                        Color(0xFF00838F),
                                        Color(0xFF0097A7),
                                      ],
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight,
                                    )
                                  : LinearGradient(
                                      colors: [
                                        Colors.grey.shade300,
                                        Colors.grey.shade400,
                                      ],
                                      begin: Alignment.centerLeft,
                                      end: Alignment.centerRight,
                                    ),
                              boxShadow: hasSelectedType
                                  ? [
                                      BoxShadow(
                                        color: const Color(0xFF00838F)
                                            .withOpacity(0.3),
                                        blurRadius: 16,
                                        offset: const Offset(0, 8),
                                      ),
                                    ]
                                  : [],
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                borderRadius: BorderRadius.circular(16),
                                onTap: hasSelectedType
                                    ? () {
                                        Navigator.pop(context);
                                        Get.to(() => UserRegistration(),
                                            arguments: {
                                              'isServiceProvider':
                                                  isServiceProvider,
                                            });
                                      }
                                    : null,
                                child: Container(
                                  alignment: Alignment.center,
                                  child: AnimatedSwitcher(
                                    duration: const Duration(milliseconds: 200),
                                    child: hasSelectedType
                                        ? Row(
                                            key: const ValueKey('enabled'),
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Text(
                                                "Continue",
                                                style: TextStyle(
                                                  fontSize: isDesktop
                                                      ? 18
                                                      : (isTablet ? 16 : 14),
                                                  fontWeight: FontWeight.w600,
                                                  color: Colors.white,
                                                  letterSpacing: 0.5,
                                                ),
                                              ),
                                              SizedBox(
                                                  width: isDesktop
                                                      ? 12
                                                      : (isTablet ? 10 : 8)),
                                              Icon(
                                                Icons.arrow_forward_rounded,
                                                color: Colors.white,
                                                size: isDesktop
                                                    ? 20
                                                    : (isTablet ? 18 : 16),
                                              ),
                                            ],
                                          )
                                        : Row(
                                            key: const ValueKey('disabled'),
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Icon(
                                                Icons.touch_app_outlined,
                                                color: Colors.grey.shade600,
                                                size: isDesktop
                                                    ? 20
                                                    : (isTablet ? 18 : 16),
                                              ),
                                              SizedBox(
                                                  width: isDesktop
                                                      ? 8
                                                      : (isTablet ? 6 : 4)),
                                              Text(
                                                "Select Account Type",
                                                style: TextStyle(
                                                  fontSize: isDesktop
                                                      ? 18
                                                      : (isTablet ? 16 : 14),
                                                  fontWeight: FontWeight.w600,
                                                  color: Colors.grey.shade600,
                                                  letterSpacing: 0.5,
                                                ),
                                              ),
                                            ],
                                          ),
                                  ),
                                ),
                              ),
                            ),
                          ),

                          SizedBox(
                              height: isDesktop ? 20 : (isTablet ? 18 : 16)),

                          // Note: Show additional hint below the disabled button
                          if (!hasSelectedType)
                            Center(
                              child: Text(
                                "Tap on User or Service Provider above to enable this button",
                                style: TextStyle(
                                  fontSize:
                                      isDesktop ? 12 : (isTablet ? 11 : 10),
                                  color: Colors.grey.shade500,
                                  fontStyle: FontStyle.italic,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),

                          if (!hasSelectedType)
                            SizedBox(
                                height: isDesktop ? 16 : (isTablet ? 14 : 12)),
                          Container(
                            height: isDesktop ? 48 : (isTablet ? 44 : 40),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.grey.shade300,
                                width: 1,
                              ),
                              color: Colors.white,
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                borderRadius: BorderRadius.circular(12),
                                onTap: () => Navigator.pop(context),
                                child: Container(
                                  alignment: Alignment.center,
                                  child: Text(
                                    "Cancel",
                                    style: TextStyle(
                                      color: const Color(0xFF64748B),
                                      fontWeight: FontWeight.w500,
                                      fontSize:
                                          isDesktop ? 16 : (isTablet ? 14 : 12),
                                      letterSpacing: 0.2,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  static Widget _buildSelectionCard({
    required bool isSelected,
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    required BuildContext context,
    bool showHint = false,
  }) {
    final size = MediaQuery.of(context).size;
    final width = size.width;
    final isDesktop = width >= 1024;
    final isTablet = width >= 600 && width < 1024;

    // Increased card height to accommodate all content properly
    final cardHeight = isDesktop ? 180.0 : (isTablet ? 170.0 : 160.0);

    Widget buildTitleWidget() {
      if (title == "Service Provider") {
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "Service",
              style: TextStyle(
                fontSize: isDesktop ? 14 : (isTablet ? 13 : 12),
                fontWeight: FontWeight.w600,
                color: isSelected
                    ? Colors.white
                    : (showHint
                        ? const Color(0xFF00838F)
                        : const Color(0xFF1A202C)),
                height: 1.1,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              "Provider",
              style: TextStyle(
                fontSize: isDesktop ? 14 : (isTablet ? 13 : 12),
                fontWeight: FontWeight.w600,
                color: isSelected
                    ? Colors.white
                    : (showHint
                        ? const Color(0xFF00838F)
                        : const Color(0xFF1A202C)),
                height: 1.1,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        );
      } else if (title == "Normal User") {
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "Normal",
              style: TextStyle(
                fontSize: isDesktop ? 14 : (isTablet ? 13 : 12),
                fontWeight: FontWeight.w600,
                color: isSelected
                    ? Colors.white
                    : (showHint
                        ? const Color(0xFF00838F)
                        : const Color(0xFF1A202C)),
                height: 1.1,
              ),
              textAlign: TextAlign.center,
            ),
            Text(
              "User",
              style: TextStyle(
                fontSize: isDesktop ? 14 : (isTablet ? 13 : 12),
                fontWeight: FontWeight.w600,
                color: isSelected
                    ? Colors.white
                    : (showHint
                        ? const Color(0xFF00838F)
                        : const Color(0xFF1A202C)),
                height: 1.1,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        );
      } else {
        return Text(
          title,
          style: TextStyle(
            fontSize: isDesktop ? 14 : (isTablet ? 13 : 12),
            fontWeight: FontWeight.w600,
            color: isSelected
                ? Colors.white
                : (showHint
                    ? const Color(0xFF00838F)
                    : const Color(0xFF1A202C)),
            height: 1.1,
          ),
          textAlign: TextAlign.center,
        );
      }
    }

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      height: cardHeight,
      decoration: BoxDecoration(
        color: isSelected ? const Color(0xFF00838F) : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected
              ? const Color(0xFF00838F)
              : (showHint
                  ? const Color(0xFF00838F).withOpacity(0.5)
                  : Colors.grey.shade300),
          width: isSelected ? 2 : (showHint ? 2 : 1),
        ),
        boxShadow: isSelected
            ? [
                BoxShadow(
                  color: const Color(0xFF00838F).withOpacity(0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ]
            : showHint
                ? [
                    BoxShadow(
                      color: const Color(0xFF00838F).withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : [],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: onTap,
          child: Container(
            padding: EdgeInsets.all(isDesktop ? 16 : (isTablet ? 14 : 12)),
            child: Column(
              mainAxisAlignment: MainAxisAlignment
                  .spaceEvenly, // Changed to spaceEvenly for better distribution
              children: [
                // Icon container
                AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  width: isDesktop
                      ? 40
                      : (isTablet ? 36 : 32), // Slightly smaller icon container
                  height: isDesktop ? 40 : (isTablet ? 36 : 32),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? Colors.white.withOpacity(0.2)
                        : (showHint
                            ? const Color(0xFF00838F).withOpacity(0.1)
                            : Colors.grey.shade100),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    icon,
                    color: isSelected
                        ? Colors.white
                        : (showHint
                            ? const Color(0xFF00838F)
                            : const Color(0xFF64748B)),
                    size: isDesktop
                        ? 20
                        : (isTablet ? 18 : 16), // Smaller icon size
                  ),
                ),

                // Title container with flexible height
                Flexible(
                  flex: 2,
                  child: Container(
                    alignment: Alignment.center,
                    child: buildTitleWidget(),
                  ),
                ),

                // Subtitle container
                Flexible(
                  flex: 1,
                  child: Container(
                    alignment: Alignment.center,
                    child: Text(
                      subtitle,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: isDesktop
                            ? 11
                            : (isTablet ? 10 : 9), // Slightly smaller font
                        color: isSelected
                            ? Colors.white.withOpacity(0.8)
                            : (showHint
                                ? const Color(0xFF00838F).withOpacity(0.7)
                                : const Color(0xFF64748B)),
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                ),

                // Hint container
                if (showHint)
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: isDesktop ? 6 : (isTablet ? 5 : 4),
                      vertical: isDesktop ? 3 : (isTablet ? 2 : 2),
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF00838F).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      "Tap to select",
                      style: TextStyle(
                        fontSize: isDesktop
                            ? 9
                            : (isTablet ? 8 : 7), // Smaller hint text
                        color: const Color(0xFF00838F),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  )
                else
                  const SizedBox(height: 16), // Maintain spacing when no hint
              ],
            ),
          ),
        ),
      ),
    );
  }
}
